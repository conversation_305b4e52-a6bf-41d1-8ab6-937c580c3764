const express = require('express');
const router = express.Router();
const userController = require('../controller/userController');
const { authenticateToken } = require('../middleware/auth'); // Auth middleware

// Get current user's details
router.get('/profile', authenticateToken, userController.getCurrentUser);

// Update current user's information
router.put('/profile', authenticateToken, userController.updateCurrentUser);

// Fetch all users (superadmin or tutor only) - MUST come before /:id route
router.get('/allUser', authenticateToken, userController.getAllUsers);

// // Search users with filters (superadmin or tutor only) - MUST come before /:id route
// router.get('/search', authenticateToken, userController.searchUsers);

// Fetch user by ID (self, superadmin, or tutor)
router.get('/:id', authenticateToken, userController.getUserById);

// Edit user (superadmin or tutor only)
router.put('/:id', authenticateToken, userController.editUser);

// Set user role (superadmin only)
router.patch('/:id/role', authenticateToken, userController.setUserRole);

// Delete user (superadmin only)
router.delete('/:id', authenticateToken, userController.deleteUser);

// userEdit profile
router.put('/profile/edit', authenticateToken, userController.updateProfile);

// Upload user avatar
router.post('/upload-avatar', authenticateToken, userController.uploadAvatar);

// Update user status (superadmin only)
router.patch('/:id/status', authenticateToken, userController.updateUserStatus);

module.exports = router;
