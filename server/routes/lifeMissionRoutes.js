const express = require('express');
const router = express.Router();
const lifeMissionController = require('../controller/lifeMissionController');

const { authenticateToken } = require('../middleware/auth');

router.post('/', authenticateToken, lifeMissionController.createLifeMission);
router.get('/my', authenticateToken, lifeMissionController.getUserLifeMissions);
router.get('/public', lifeMissionController.getPublicLifeMissions);
router.get('/:id', authenticateToken, lifeMissionController.getLifeMissionById);
router.delete(
	'/:id',
	authenticateToken,
	lifeMissionController.deleteLifeMission,
);
// Interaction routes
router.put('/:id/like', authenticateToken, lifeMissionController.toggleLike);
router.put(
	'/:id/bookmark',
	authenticateToken,
	lifeMissionController.toggleBookmark,
);
router.post(
	'/:id/comments',
	authenticateToken,
	lifeMissionController.addComment,
);
router.delete(
	'/:id/comments/:commentId',
	authenticateToken,
	lifeMissionController.deleteComment,
);

module.exports = router;
