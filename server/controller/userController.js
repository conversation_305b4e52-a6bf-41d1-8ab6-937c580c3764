const User = require('../models/User');

// Fetch user by ID (self, superadmin, or tutor)
exports.getUserById = async (req, res) => {
	try {
		const { id } = req.params;
		// Allow self, superadmin, or tutor
		if (
			req.user.id !== id &&
			!req.user.roles.includes('superadmin') &&
			!req.user.roles.includes('tutor')
		) {
			return res.status(403).json({ success: false, message: 'Forbidden' });
		}
		const user = await User.findById(id).select('-password');
		if (!user) {
			return res
				.status(404)
				.json({ success: false, message: 'User not found' });
		}
		res.json({ success: true, user });
	} catch (error) {
		res.status(500).json({ success: false, message: error.message });
	}
};

// Fetch all users
exports.getAllUsers = async (req, res) => {
	const { page = 1, limit = 20, search = '' } = req.query;
	try {
		if (!req.user) {
			return res.status(403).json({ success: false, message: 'Forbidden' });
		}

		const pageNum = parseInt(page, 10);
		const limitNum = parseInt(limit, 10);
		const skip = (pageNum - 1) * limitNum;

		// Build search query
		let query = {};
		if (search) {
			query = {
				$or: [
					{ name: { $regex: search, $options: 'i' } },
					{ email: { $regex: search, $options: 'i' } },
				],
			};
		}

		const users = await User.find(query)
			.select('-password -mobile -dateOfBirth -location.address')
			.skip(skip)
			.limit(limitNum)
			.sort({ createdAt: -1 });

		const total = await User.countDocuments(query);

		res.json({
			success: true,
			data: users,
			pagination: {
				total,
				page: Number(page),
				limit: Number(limit),
				totalPages: Math.ceil(total / limit),
			},
		});
	} catch (error) {
		res.status(500).json({ success: false, message: error.message });
	}
};

// Edit user (superadmin or tutor only)
exports.editUser = async (req, res) => {
	try {
		const { id } = req.params;
		const updates = req.body;
		if (
			!req.user.roles.includes('superadmin') &&
			!req.user.roles.includes('tutor')
		) {
			return res.status(403).json({ success: false, message: 'Forbidden' });
		}
		const user = await User.findByIdAndUpdate(id, updates, {
			new: true,
			runValidators: true,
		}).select('-password');
		if (!user) {
			return res
				.status(404)
				.json({ success: false, message: 'User not found' });
		}
		res.json({ success: true, user });
	} catch (error) {
		res.status(500).json({ success: false, message: error.message });
	}
};

// Set user role (superadmin only)
exports.setUserRole = async (req, res) => {
	try {
		const { id } = req.params;
		const { roles } = req.body;
		if (!req.user.roles.includes('superadmin')) {
			return res.status(403).json({ success: false, message: 'Forbidden' });
		}
		if (!Array.isArray(roles) || roles.length === 0) {
			return res
				.status(400)
				.json({ success: false, message: 'Roles must be a non-empty array' });
		}
		const user = await User.findByIdAndUpdate(
			id,
			{ roles },
			{ new: true, runValidators: true },
		).select('-password');
		if (!user) {
			return res
				.status(404)
				.json({ success: false, message: 'User not found' });
		}
		res.json({ success: true, user });
	} catch (error) {
		res.status(500).json({ success: false, message: error.message });
	}
};

// Get current user's details (secured by auth middleware)
exports.getCurrentUser = async (req, res) => {
	try {
		const user = await User.findById(req.user.id).select('-password');
		if (!user) {
			return res
				.status(404)
				.json({ success: false, message: 'User not found' });
		}
		res.json({ success: true, user });
	} catch (error) {
		res.status(500).json({ success: false, message: error.message });
	}
};

// Update current user's information (secured by auth middleware)
exports.updateCurrentUser = async (req, res) => {
	try {
		const updates = req.body;
		const user = await User.findByIdAndUpdate(req.user.id, updates, {
			new: true,
			runValidators: true,
		}).select('-password');
		if (!user) {
			return res
				.status(404)
				.json({ success: false, message: 'User not found' });
		}
		res.json({ success: true, user });
	} catch (error) {
		res.status(500).json({ success: false, message: error.message });
	}
};

// Update current user's profile (name, dob, location, mobile, aboutMe, avatar)
exports.updateProfile = async (req, res) => {
	try {
		const { name, dateOfBirth, location, mobile, aboutMe, avatar } = req.body;

		// Build update object with only provided fields
		const updates = {};

		if (name !== undefined) {
			if (!name || name.trim().length === 0) {
				return res.status(400).json({
					success: false,
					message: 'Name cannot be empty',
				});
			}
			updates.name = name.trim();
		}

		if (dateOfBirth !== undefined) {
			if (dateOfBirth && new Date(dateOfBirth) > new Date()) {
				return res.status(400).json({
					success: false,
					message: 'Date of birth cannot be in the future',
				});
			}
			updates.dateOfBirth = dateOfBirth;
		}

		if (location !== undefined) {
			updates.location = location;
		}

		if (mobile !== undefined) {
			if (mobile && !/^[\+]?[0-9\s\-\(\)]{10,15}$/.test(mobile)) {
				return res.status(400).json({
					success: false,
					message: 'Please enter a valid mobile number',
				});
			}
			updates.mobile = mobile;
		}

		if (aboutMe !== undefined) {
			if (aboutMe && aboutMe.length > 500) {
				return res.status(400).json({
					success: false,
					message: 'About me cannot exceed 500 characters',
				});
			}
			updates.aboutMe = aboutMe;
		}

		if (avatar !== undefined) {
			// Validate avatar URL if provided
			if (avatar && typeof avatar === 'string') {
				// Basic URL validation - check if it's a valid URL format
				try {
					new URL(avatar);
					updates.avatar = avatar;
				} catch (urlError) {
					return res.status(400).json({
						success: false,
						message: 'Please provide a valid avatar URL',
					});
				}
			} else if (avatar === null || avatar === '') {
				// Allow removing avatar by setting to null
				updates.avatar = null;
			}
		}

		// If no valid updates provided
		if (Object.keys(updates).length === 0) {
			return res.status(400).json({
				success: false,
				message: 'No valid updates provided',
			});
		}

		const user = await User.findByIdAndUpdate(req.user.id, updates, {
			new: true,
			runValidators: true,
		}).select('-password');

		if (!user) {
			return res.status(404).json({
				success: false,
				message: 'User not found',
			});
		}

		res.json({
			success: true,
			message: 'Profile updated successfully',
			user,
		});
	} catch (error) {
		// Handle mongoose validation errors
		if (error.name === 'ValidationError') {
			const errors = Object.values(error.errors).map((err) => err.message);
			return res.status(400).json({
				success: false,
				message: 'Validation error',
				errors,
			});
		}

		res.status(500).json({ success: false, message: error.message });
	}
};

// Delete user (superadmin only)
exports.deleteUser = async (req, res) => {
	try {
		const { id } = req.params;
		if (!req.user.roles.includes('superadmin')) {
			return res.status(403).json({ success: false, message: 'Forbidden' });
		}

		// Prevent self-deletion
		if (req.user.id === id) {
			return res.status(400).json({
				success: false,
				message: 'Cannot delete your own account',
			});
		}

		const user = await User.findByIdAndDelete(id);
		if (!user) {
			return res
				.status(404)
				.json({ success: false, message: 'User not found' });
		}
		res.json({ success: true, message: 'User deleted successfully' });
	} catch (error) {
		res.status(500).json({ success: false, message: error.message });
	}
};

// Upload user avatar
exports.uploadAvatar = async (req, res) => {
	try {
		const { avatar } = req.body;

		// Validate avatar URL
		if (!avatar || typeof avatar !== 'string') {
			return res.status(400).json({
				success: false,
				message: 'Avatar URL is required',
			});
		}

		// Basic URL validation
		try {
			new URL(avatar);
		} catch (urlError) {
			return res.status(400).json({
				success: false,
				message: 'Please provide a valid avatar URL',
			});
		}

		// Update user's avatar
		const user = await User.findByIdAndUpdate(
			req.user.id,
			{ avatar },
			{ new: true, runValidators: true },
		).select('-password');

		if (!user) {
			return res.status(404).json({
				success: false,
				message: 'User not found',
			});
		}

		res.json({
			success: true,
			message: 'Avatar updated successfully',
			user,
			avatarUrl: avatar,
		});
	} catch (error) {
		res.status(500).json({
			success: false,
			message: error.message,
		});
	}
};

// Update user status (superadmin only)
exports.updateUserStatus = async (req, res) => {
	try {
		const { id } = req.params;
		const { status } = req.body;

		if (!req.user.roles.includes('superadmin')) {
			return res.status(403).json({ success: false, message: 'Forbidden' });
		}

		if (!['active', 'inactive', 'suspended'].includes(status)) {
			return res.status(400).json({
				success: false,
				message: 'Invalid status. Must be active, inactive, or suspended',
			});
		}

		const user = await User.findByIdAndUpdate(
			id,
			{ status },
			{ new: true, runValidators: true },
		).select('-password');

		if (!user) {
			return res
				.status(404)
				.json({ success: false, message: 'User not found' });
		}

		res.json({ success: true, user });
	} catch (error) {
		res.status(500).json({ success: false, message: error.message });
	}
};

// Export users to CSV (superadmin only)
exports.exportUsers = async (req, res) => {
	try {
		if (!req.user.roles.includes('superadmin')) {
			return res.status(403).json({ success: false, message: 'Forbidden' });
		}

		const { format = 'csv', role, status } = req.query;

		// Build query for export
		let query = {};
		if (role) query.roles = role;
		if (status) query.status = status;

		const users = await User.find(query)
			.select(
				'-password -resetPasswordToken -resetPasswordExpires -twoFactorSecret',
			)
			.lean();

		if (format === 'csv') {
			// Generate CSV with updated fields
			const csvHeader =
				'ID,Name,Email,Mobile,Date of Birth,Country,State,City,Address,About Me,Roles,Status,Email Verified,Last Login,Created At\n';
			const csvData = users
				.map((user) => {
					return [
						user._id,
						`"${user.name || ''}"`,
						user.email,
						`"${user.mobile || ''}"`,
						user.dateOfBirth
							? new Date(user.dateOfBirth).toISOString().split('T')[0]
							: '',
						`"${user.location?.country || ''}"`,
						`"${user.location?.state || ''}"`,
						`"${user.location?.city || ''}"`,
						`"${user.location?.address || ''}"`,
						`"${(user.aboutMe || '').replace(/"/g, '""')}"`, // Escape quotes in aboutMe
						`"${user.roles.join(', ')}"`,
						user.status,
						user.isEmailVerified,
						user.lastLogin ? new Date(user.lastLogin).toISOString() : '',
						new Date(user.createdAt).toISOString(),
					].join(',');
				})
				.join('\n');

			res.setHeader('Content-Type', 'text/csv');
			res.setHeader(
				'Content-Disposition',
				`attachment; filename="users-export-${Date.now()}.csv"`,
			);
			res.send(csvHeader + csvData);
		} else {
			// Return JSON
			res.json({
				success: true,
				users,
				exportedAt: new Date().toISOString(),
				totalUsers: users.length,
			});
		}
	} catch (error) {
		res.status(500).json({ success: false, message: error.message });
	}
};
