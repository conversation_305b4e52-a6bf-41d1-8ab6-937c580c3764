const Value = require('../models/Value');
const { validationResult } = require('express-validator');

/**
 * Create a new value
 */
exports.createValue = async (req, res) => {
	try {
		const errors = validationResult(req);
		if (!errors.isEmpty()) {
			return res.status(400).json({
				success: false,
				message: 'Validation failed',
				errors: errors.array(),
			});
		}

		const { name, definition, isPublic = false } = req.body;
		const userId = req.user._id;

		// Check if value with same name already exists for this user
		const existingValue = await Value.findOne({
			userId,
			name: { $regex: new RegExp(`^${name}$`, 'i') },
		});

		if (existingValue) {
			return res.status(400).json({
				success: false,
				message: 'A value with this name already exists',
			});
		}

		const value = new Value({
			userId,
			name: name.trim(),
			definition: definition,
			isPublic,
		});

		await value.save();

		res.status(201).json({
			success: true,
			message: 'Value created successfully',
			data: {
				value,
			},
		});
	} catch (error) {
		console.error('Create Value Error:', error);
		res.status(500).json({
			success: false,
			message: 'Failed to create value',
		});
	}
};

/**
 * Get all values for the authenticated user
 */
exports.getUserValues = async (req, res) => {
	try {
		const userId = req.user._id;
		const { page = 1, limit = 10, search } = req.query;

		const query = { userId };
		if (search) {
			query.$or = [
				{ name: { $regex: search, $options: 'i' } },
				{ definition: { $regex: search, $options: 'i' } },
			];
		}

		const options = {
			page: parseInt(page),
			limit: parseInt(limit),
			sort: { createdAt: -1 },
		};

		const values = await Value.find(query)
			.sort(options.sort)
			.limit(options.limit * options.page)
			.skip((options.page - 1) * options.limit)
			.populate('userId', 'name email');

		const total = await Value.countDocuments(query);

		res.status(200).json({
			success: true,
			message: 'Values retrieved successfully',
			data: {
				values,
				pagination: {
					page: options.page,
					limit: options.limit,
					total,
					totalPages: Math.ceil(total / options.limit),
					hasNext: options.page < Math.ceil(total / options.limit),
					hasPrev: options.page > 1,
				},
			},
		});
	} catch (error) {
		console.error('Get User Values Error:', error);
		res.status(500).json({
			success: false,
			message: 'Failed to retrieve values',
		});
	}
};

/**
 * Get public values from all users
 */
exports.getPublicValues = async (req, res) => {
	try {
		const { page = 1, limit = 10, search } = req.query;

		const query = { isPublic: true };
		if (search) {
			query.$or = [
				{ name: { $regex: search, $options: 'i' } },
				{ definition: { $regex: search, $options: 'i' } },
			];
		}

		const options = {
			page: parseInt(page),
			limit: parseInt(limit),
			sort: { createdAt: -1 },
		};

		const values = await Value.find(query)
			.sort(options.sort)
			.limit(options.limit * options.page)
			.skip((options.page - 1) * options.limit)
			.populate('userId', 'name _id avatar');

		const total = await Value.countDocuments(query);

		res.status(200).json({
			success: true,
			message: 'Public values retrieved successfully',
			data: {
				values,
				pagination: {
					page: options.page,
					limit: options.limit,
					total,
					totalPages: Math.ceil(total / options.limit),
					hasNext: options.page < Math.ceil(total / options.limit),
					hasPrev: options.page > 1,
				},
			},
		});
	} catch (error) {
		console.error('Get Public Values Error:', error);
		res.status(500).json({
			success: false,
			message: 'Failed to retrieve public values',
		});
	}
};

/**
 * Get a specific value by ID
 */
exports.getValueById = async (req, res) => {
	try {
		const { id } = req.params;

		const value = await Value.findById(id).populate('userId', 'name');
		if (!value) {
			return res.status(404).json({
				success: false,
				message: 'Value not found',
			});
		}

		// Allow access if public or belongs to current user
		if (
			!value.isPublic &&
			value.userId._id.toString() !== req.user._id.toString()
		) {
			return res.status(403).json({
				success: false,
				message: 'Unauthorized',
			});
		}

		res.status(200).json({
			success: true,
			data: value,
		});
	} catch (error) {
		console.error('Get Value By ID Error:', error);
		res.status(500).json({
			success: false,
			message: 'Failed to retrieve value',
		});
	}
};

/**
 * Update a value
 */
exports.updateValue = async (req, res) => {
	try {
		const errors = validationResult(req);
		if (!errors.isEmpty()) {
			return res.status(400).json({
				success: false,
				message: 'Validation failed',
				errors: errors.array(),
			});
		}

		const { id } = req.params;
		const userId = req.user._id;
		const { name, definition, isPublic } = req.body;

		// Find the value and ensure user owns it
		const value = await Value.findOne({ _id: id, userId });
		if (!value) {
			return res.status(404).json({
				success: false,
				message: 'Value not found or access denied',
			});
		}

		// Check if another value with the same name exists (excluding current one)
		if (name && name !== value.name) {
			const existingValue = await Value.findOne({
				userId,
				name: { $regex: new RegExp(`^${name}$`, 'i') },
				_id: { $ne: id },
			});

			if (existingValue) {
				return res.status(400).json({
					success: false,
					message: 'A value with this name already exists',
				});
			}
		}

		// Update fields
		if (name) value.name = name.trim();
		if (definition) value.definition = definition.trim();
		if (typeof isPublic === 'boolean') value.isPublic = isPublic;

		await value.save();

		res.status(200).json({
			success: true,
			message: 'Value updated successfully',
			data: {
				value,
			},
		});
	} catch (error) {
		console.error('Update Value Error:', error);
		res.status(500).json({
			success: false,
			message: 'Failed to update value',
		});
	}
};

/**
 * Delete a value
 */
exports.deleteValue = async (req, res) => {
	try {
		const { id } = req.params;
		const userId = req.user._id;

		const value = await Value.findOneAndDelete({ _id: id, userId });
		if (!value) {
			return res.status(404).json({
				success: false,
				message: 'Value not found or access denied',
			});
		}

		res.status(200).json({
			success: true,
			message: 'Value deleted successfully',
			data: {
				value,
			},
		});
	} catch (error) {
		console.error('Delete Value Error:', error);
		res.status(500).json({
			success: false,
			message: 'Failed to delete value',
		});
	}
};
/**
 * Like or Unlike a value
 */
exports.toggleLikeValue = async (req, res) => {
	try {
		const { id } = req.params;
		const userId = req.user._id;

		const value = await Value.findById(id);
		if (!value) {
			return res
				.status(404)
				.json({ success: false, message: 'Value not found' });
		}

		const alreadyLiked = value.likes.includes(userId);
		if (alreadyLiked) {
			value.likes.pull(userId);
		} else {
			value.likes.push(userId);
		}

		await value.save();
		res.status(200).json({
			success: true,
			message: alreadyLiked ? 'Unliked' : 'Liked',
			data: { likesCount: value.likes.length },
		});
	} catch (error) {
		console.error('Toggle Like Error:', error);
		res.status(500).json({ success: false, message: 'Failed to toggle like' });
	}
};
/**
 * Bookmark or Unbookmark a value
 */
exports.toggleBookmarkValue = async (req, res) => {
	try {
		const { id } = req.params;
		const userId = req.user._id;

		const value = await Value.findById(id);
		if (!value) {
			return res
				.status(404)
				.json({ success: false, message: 'Value not found' });
		}

		const alreadyBookmarked = value.bookmarks.includes(userId);
		if (alreadyBookmarked) {
			value.bookmarks.pull(userId);
		} else {
			value.bookmarks.push(userId);
		}

		await value.save();
		res.status(200).json({
			success: true,
			message: alreadyBookmarked ? 'Unbookmarked' : 'Bookmarked',
			data: { bookmarksCount: value.bookmarks.length },
		});
	} catch (error) {
		console.error('Toggle Bookmark Error:', error);
		res
			.status(500)
			.json({ success: false, message: 'Failed to toggle bookmark' });
	}
};
/**
 * Add a comment to a value
 */
exports.addCommentToValue = async (req, res) => {
	try {
		const { id } = req.params;
		const { text } = req.body;
		const userId = req.user._id;

		if (!text || !text.trim()) {
			return res
				.status(400)
				.json({ success: false, message: 'Comment cannot be empty' });
		}

		const value = await Value.findById(id);
		if (!value) {
			return res
				.status(404)
				.json({ success: false, message: 'Value not found' });
		}

		const userName = req.user.name;

		value.comments.unshift({
			userId,
			userName,
			text: text.trim(),
		});
		await value.save();

		res.status(201).json({
			success: true,
			message: 'Comment added',
			data: value.comments,
		});
	} catch (error) {
		console.error('Add Comment Error:', error);
		res.status(500).json({ success: false, message: 'Failed to add comment' });
	}
};

/**
 * Delete a comment from a value
 */
exports.deleteCommentFromValue = async (req, res) => {
	try {
		const { id, commentId } = req.params;
		const userId = req.user._id;

		const value = await Value.findById(id);
		if (!value) {
			return res
				.status(404)
				.json({ success: false, message: 'Value not found' });
		}

		const comment = value.comments.id(commentId);
		if (!comment) {
			return res
				.status(404)
				.json({ success: false, message: 'Comment not found' });
		}

		// Only comment author or value owner can delete
		if (!comment.userId.equals(userId) && !value.userId.equals(userId)) {
			return res.status(403).json({
				success: false,
				message: 'Not authorized to delete this comment',
			});
		}

		comment.deleteOne();
		await value.save();

		res.status(200).json({
			success: true,
			message: 'Comment deleted successfully',
		});
	} catch (error) {
		console.error('Delete Comment Error:', error);
		res
			.status(500)
			.json({ success: false, message: 'Failed to delete comment' });
	}
};
