const Goal = require('../models/Goal');
const { validationResult } = require('express-validator');

/**
 * Create a new goal
 */
exports.createGoal = async (req, res) => {
	try {
		const errors = validationResult(req);
		if (!errors.isEmpty()) {
			return res.status(400).json({
				success: false,
				message: 'Validation failed',
				errors: errors.array(),
			});
		}

		const { title, tasks = [], isPublic = false } = req.body;
		const userId = req.user._id;

		// Check if goal with same title already exists for this user
		const existingGoal = await Goal.findOne({
			userId,
			title: { $regex: new RegExp(`^${title}$`, 'i') },
		});

		if (existingGoal) {
			return res.status(400).json({
				success: false,
				message: 'A goal with this title already exists',
			});
		}

		const goal = new Goal({
			userId,
			title: title.trim(),
			tasks,
			isPublic,
		});

		await goal.save();

		res.status(201).json({
			success: true,
			message: 'Goal created successfully',
			data: {
				goal,
			},
		});
	} catch (error) {
		console.error('Create Goal Error:', error);
		res.status(500).json({
			success: false,
			message: 'Failed to create goal',
		});
	}
};

/**
 * Get all goals for the authenticated user
 */
exports.getUserGoals = async (req, res) => {
	try {
		const userId = req.user._id;
		const { page = 1, limit = 10, search, status } = req.query;

		const query = { userId };
		if (search) {
			query.$or = [
				{ title: { $regex: search, $options: 'i' } },
				{ 'tasks.name': { $regex: search, $options: 'i' } },
			];
		}

		const options = {
			page: parseInt(page),
			limit: parseInt(limit),
			sort: { createdAt: -1 },
		};

		const goals = await Goal.find(query)
			.sort(options.sort)
			.limit(options.limit * options.page)
			.skip((options.page - 1) * options.limit)
			.populate('userId', 'name email');

		const total = await Goal.countDocuments(query);

		res.status(200).json({
			success: true,
			message: 'Goals retrieved successfully',
			data: {
				goals,
				pagination: {
					page: options.page,
					limit: options.limit,
					total,
					totalPages: Math.ceil(total / options.limit),
					hasNext: options.page < Math.ceil(total / options.limit),
					hasPrev: options.page > 1,
				},
			},
		});
	} catch (error) {
		console.error('Get User Goals Error:', error);
		res.status(500).json({
			success: false,
			message: 'Failed to retrieve goals',
		});
	}
};

/**
 * Get public goals from all users
 */
exports.getPublicGoals = async (req, res) => {
	try {
		const { page = 1, limit = 10, search } = req.query;

		const query = { isPublic: true };
		if (search) {
			query.$or = [
				{ title: { $regex: search, $options: 'i' } },
				{ 'tasks.name': { $regex: search, $options: 'i' } },
			];
		}

		const options = {
			page: parseInt(page),
			limit: parseInt(limit),
			sort: { createdAt: -1 },
		};

		const goals = await Goal.find(query)
			.sort(options.sort)
			.limit(options.limit * options.page)
			.skip((options.page - 1) * options.limit)
			.populate('userId', 'name _id avatar');

		const total = await Goal.countDocuments(query);

		res.status(200).json({
			success: true,
			message: 'Public goals retrieved successfully',
			data: {
				goals,
				pagination: {
					page: options.page,
					limit: options.limit,
					total,
					totalPages: Math.ceil(total / options.limit),
					hasNext: options.page < Math.ceil(total / options.limit),
					hasPrev: options.page > 1,
				},
			},
		});
	} catch (error) {
		console.error('Get Public Goals Error:', error);
		res.status(500).json({
			success: false,
			message: 'Failed to retrieve public goals',
		});
	}
};

/**
 * Get a specific goal by ID
 */
exports.getGoalById = async (req, res) => {
	try {
		const { id } = req.params;

		const goal = await Goal.findById(id).populate('userId', 'name');
		if (!goal) {
			return res.status(404).json({
				success: false,
				message: 'Goal not found',
			});
		}

		// Allow access if public or belongs to current user
		if (
			!goal.isPublic &&
			goal.userId._id.toString() !== req.user._id.toString()
		) {
			return res.status(403).json({
				success: false,
				message: 'Unauthorized',
			});
		}

		res.status(200).json({
			success: true,
			data: goal,
		});
	} catch (error) {
		console.error('Get Goal By ID Error:', error);
		res.status(500).json({
			success: false,
			message: 'Failed to retrieve goal',
		});
	}
};

/**
 * Update a goal
 */
exports.updateGoal = async (req, res) => {
	try {
		const errors = validationResult(req);
		if (!errors.isEmpty()) {
			return res.status(400).json({
				success: false,
				message: 'Validation failed',
				errors: errors.array(),
			});
		}

		const { id } = req.params;
		const userId = req.user._id;
		const { title, tasks, isPublic } = req.body;

		// Find the goal and ensure user owns it
		const goal = await Goal.findOne({ _id: id, userId });
		if (!goal) {
			return res.status(404).json({
				success: false,
				message: 'Goal not found or access denied',
			});
		}

		// Check if another goal with the same title exists (excluding current one)
		if (title && title !== goal.title) {
			const existingGoal = await Goal.findOne({
				userId,
				title: { $regex: new RegExp(`^${title}$`, 'i') },
				_id: { $ne: id },
			});

			if (existingGoal) {
				return res.status(400).json({
					success: false,
					message: 'A goal with this title already exists',
				});
			}
		}

		// Update fields
		if (title) goal.title = title.trim();
		if (tasks) goal.tasks = tasks;
		if (typeof isPublic === 'boolean') goal.isPublic = isPublic;

		await goal.save();

		res.status(200).json({
			success: true,
			message: 'Goal updated successfully',
			data: {
				goal,
			},
		});
	} catch (error) {
		console.error('Update Goal Error:', error);
		res.status(500).json({
			success: false,
			message: 'Failed to update goal',
		});
	}
};

/**
 * Update task status within a goal
 */
exports.updateTaskStatus = async (req, res) => {
	try {
		const { id, taskId } = req.params;
		const { status } = req.body;
		const userId = req.user._id;

		// Validate status
		const validStatuses = ['Not Started', 'In Progress', 'Completed'];
		if (!validStatuses.includes(status)) {
			return res.status(400).json({
				success: false,
				message: 'Invalid task status',
			});
		}

		const goal = await Goal.findOne({ _id: id, userId });
		if (!goal) {
			return res.status(404).json({
				success: false,
				message: 'Goal not found or access denied',
			});
		}

		// Find the task
		const task = goal.tasks.id(taskId);
		if (!task) {
			return res.status(404).json({
				success: false,
				message: 'Task not found',
			});
		}

		// Set originalCategory if not already set (for backward compatibility)
		if (!task.originalCategory) {
			task.originalCategory = task.category || 'amber';
		}

		// Update task status
		task.status = status;

		// Automatically move completed tasks to green section
		// and move uncompleted tasks back to their original category
		if (status === 'Completed') {
			task.category = 'green';
		} else if (task.category === 'green' && status !== 'Completed') {
			// If task was in green section but is no longer completed,
			// move it back to its original category
			task.category = task.originalCategory;
		}

		await goal.save();

		res.status(200).json({
			success: true,
			message: 'Task status updated successfully',
			data: {
				goal,
			},
		});
	} catch (error) {
		console.error('Update Task Status Error:', error);
		res.status(500).json({
			success: false,
			message: 'Failed to update task status',
		});
	}
};

/**
 * Add a new task to a goal
 */
exports.addTask = async (req, res) => {
	try {
		const { id } = req.params;
		const { name, status = 'Not Started', category = 'amber' } = req.body;
		const userId = req.user._id;

		if (!name || !name.trim()) {
			return res.status(400).json({
				success: false,
				message: 'Task name is required',
			});
		}

		const goal = await Goal.findOne({ _id: id, userId });
		if (!goal) {
			return res.status(404).json({
				success: false,
				message: 'Goal not found or access denied',
			});
		}

		// Add new task
		goal.tasks.push({
			name: name.trim(),
			status,
			category,
			originalCategory: category, // Store the original category
		});

		await goal.save();

		res.status(201).json({
			success: true,
			message: 'Task added successfully',
			data: {
				goal,
			},
		});
	} catch (error) {
		console.error('Add Task Error:', error);
		res.status(500).json({
			success: false,
			message: 'Failed to add task',
		});
	}
};

/**
 * Delete a task from a goal
 */
exports.deleteTask = async (req, res) => {
	try {
		const { id, taskId } = req.params;
		const userId = req.user._id;

		const goal = await Goal.findOne({ _id: id, userId });
		if (!goal) {
			return res.status(404).json({
				success: false,
				message: 'Goal not found or access denied',
			});
		}

		// Remove the task
		goal.tasks.pull(taskId);
		await goal.save();

		res.status(200).json({
			success: true,
			message: 'Task deleted successfully',
			data: {
				goal,
			},
		});
	} catch (error) {
		console.error('Delete Task Error:', error);
		res.status(500).json({
			success: false,
			message: 'Failed to delete task',
		});
	}
};

/**
 * Delete a goal
 */
exports.deleteGoal = async (req, res) => {
	try {
		const { id } = req.params;
		const userId = req.user._id;

		const goal = await Goal.findOneAndDelete({ _id: id, userId });
		if (!goal) {
			return res.status(404).json({
				success: false,
				message: 'Goal not found or access denied',
			});
		}

		res.status(200).json({
			success: true,
			message: 'Goal deleted successfully',
			data: {
				goal,
			},
		});
	} catch (error) {
		console.error('Delete Goal Error:', error);
		res.status(500).json({
			success: false,
			message: 'Failed to delete goal',
		});
	}
};
/**
 * Like or unlike a goal
 */
exports.toggleLikeGoal = async (req, res) => {
	try {
		const { id } = req.params;
		const userId = req.user._id;

		const goal = await Goal.findById(id);
		if (!goal || (!goal.isPublic && !goal.userId.equals(userId))) {
			return res
				.status(404)
				.json({ success: false, message: 'Goal not found or access denied' });
		}

		const alreadyLiked = goal.likes.includes(userId);
		if (alreadyLiked) {
			goal.likes.pull(userId);
		} else {
			goal.likes.push(userId);
		}

		await goal.save();
		res.status(200).json({
			success: true,
			message: alreadyLiked ? 'Goal unliked' : 'Goal liked',
			data: { likesCount: goal.likes.length },
		});
	} catch (err) {
		console.error('Toggle Like Error:', err);
		res.status(500).json({ success: false, message: 'Failed to toggle like' });
	}
};

/**
 * Bookmark or remove bookmark
 */
exports.toggleBookmarkGoal = async (req, res) => {
	try {
		const { id } = req.params;
		const userId = req.user._id;

		const goal = await Goal.findById(id);
		if (!goal || (!goal.isPublic && !goal.userId.equals(userId))) {
			return res
				.status(404)
				.json({ success: false, message: 'Goal not found or access denied' });
		}

		const alreadyBookmarked = goal.bookmarks.includes(userId);
		if (alreadyBookmarked) {
			goal.bookmarks.pull(userId);
		} else {
			goal.bookmarks.push(userId);
		}

		await goal.save();
		res.status(200).json({
			success: true,
			message: alreadyBookmarked ? 'Bookmark removed' : 'Goal bookmarked',
			data: { bookmarksCount: goal.bookmarks.length },
		});
	} catch (err) {
		console.error('Toggle Bookmark Error:', err);
		res
			.status(500)
			.json({ success: false, message: 'Failed to toggle bookmark' });
	}
};

/**
 * Add a comment to a goal
 */
exports.addCommentToGoal = async (req, res) => {
	try {
		const { id } = req.params;
		const { text } = req.body;
		const userId = req.user._id;

		if (!text || !text.trim()) {
			return res
				.status(400)
				.json({ success: false, message: 'Comment text is required' });
		}

		const goal = await Goal.findById(id);
		if (!goal || (!goal.isPublic && !goal.userId.equals(userId))) {
			return res
				.status(404)
				.json({ success: false, message: 'Goal not found or access denied' });
		}

		const userName = req.user.name;

		goal.comments.push({
			userId,
			userName,
			text: text.trim(),
		});
		await goal.save();

		res.status(201).json({
			success: true,
			message: 'Comment added',
			data: goal.comments,
		});
	} catch (err) {
		console.error('Add Comment Error:', err);
		res.status(500).json({ success: false, message: 'Failed to add comment' });
	}
};
/**
 * Delete a comment from a goal
 */
exports.deleteCommentFromGoal = async (req, res) => {
	try {
		const { id, commentId } = req.params;
		const userId = req.user._id;

		const goal = await Goal.findById(id);
		if (!goal) {
			return res
				.status(404)
				.json({ success: false, message: 'Goal not found' });
		}

		const comment = goal.comments.id(commentId);
		if (!comment) {
			return res
				.status(404)
				.json({ success: false, message: 'Comment not found' });
		}

		// Only comment owner or goal owner can delete
		if (!comment.userId.equals(userId) && !goal.userId.equals(userId)) {
			return res.status(403).json({
				success: false,
				message: 'Not authorized to delete this comment',
			});
		}
		comment.deleteOne();
		await goal.save();

		res.status(200).json({
			success: true,
			message: 'Comment deleted successfully',
			data: { comments: goal.comments },
		});
	} catch (error) {
		console.error('Delete Comment Error:', error);
		res
			.status(500)
			.json({ success: false, message: 'Failed to delete comment' });
	}
};
