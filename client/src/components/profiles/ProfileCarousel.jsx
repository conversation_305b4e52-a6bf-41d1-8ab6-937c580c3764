'use client';
import { useState, useEffect } from 'react';
import {
	ChevronLeft,
	ChevronRight,
	Heart,
	MessageCircle,
	User,
} from 'lucide-react';
import Image from 'next/image';
import { usePaginatedProfiles } from '@/src/lib/hooks/usePaginatedProfiles';

const ProfileCarousel = () => {
	const { profiles, loading, error, loadMore, hasMore } =
		usePaginatedProfiles();

	const [currentIndex, setCurrentIndex] = useState(0);

	const currentProfile = profiles[currentIndex];

	const handleNext = () => {
		const nextIndex = currentIndex + 1;

		// If user is nearing the end of the list, prefetch next page
		if (nextIndex >= profiles.length - 2 && hasMore) {
			loadMore();
		}

		if (nextIndex < profiles.length) {
			setCurrentIndex(nextIndex);
		}
	};

	const handlePrev = () => {
		if (currentIndex > 0) {
			setCurrentIndex(currentIndex - 1);
		}
	};

	useEffect(() => {
		if (!currentProfile && profiles.length > 0) {
			setCurrentIndex(0);
		}
	}, [profiles]);

	if (loading && profiles.length === 0) {
		return (
			<p className='text-center py-12 text-gray-500'>Loading profiles...</p>
		);
	}

	if (error) {
		return (
			<p className='text-center py-12 text-red-500'>
				Failed to load profiles. Try again later.
			</p>
		);
	}

	if (!currentProfile) {
		return (
			<p className='text-center py-12 text-gray-500'>No profiles available.</p>
		);
	}

	return (
		<div className='flex items-center justify-center'>
			<div className='relative bg-white rounded-3xl shadow-2xl overflow-hidden max-w-sm w-full'>
				{/* Profile Image */}
				<div className='relative h-96 bg-gradient-to-b from-amber-100 to-amber-200'>
					<Image
						src={
							currentProfile.avatar ||
							`https://ui-avatars.com/api/?name=${encodeURIComponent(
								currentProfile.name || 'Anonymous',
							)}&background=random`
						}
						alt={currentProfile.name}
						fill
						className='object-cover'
					/>

					{/* Arrows */}
					{currentIndex > 0 && (
						<button
							onClick={handlePrev}
							className='absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 shadow-lg transition-all duration-200 hover:scale-110'>
							<ChevronLeft className='w-6 h-6 text-gray-700' />
						</button>
					)}

					{currentIndex < profiles.length - 1 && (
						<button
							onClick={handleNext}
							className='absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 shadow-lg transition-all duration-200 hover:scale-110'>
							<ChevronRight className='w-6 h-6 text-gray-700' />
						</button>
					)}
				</div>

				{/* Profile Info */}
				<div className='p-6 bg-white'>
					<div className='text-center mb-6'>
						<h2 className='text-2xl font-bold text-gray-800 mb-1'>
							{currentProfile.name}
						</h2>
					</div>

					<div className='flex space-x-3'>
						<button className='flex items-center justify-center w-16 h-16 bg-gray-100 hover:bg-gray-200 rounded-full transition-all duration-200 hover:scale-105'>
							<Heart className='w-6 h-6 text-gray-600' />
						</button>

						<button className='flex-1 bg-slate-800 hover:bg-slate-700 text-white py-4 px-6 rounded-full font-semibold transition-all duration-200 hover:scale-105 flex items-center justify-center space-x-2'>
							<MessageCircle className='w-5 h-5' />
							<span>Message</span>
						</button>
					</div>

					<button className='w-full mt-3 bg-teal-500 hover:bg-teal-600 text-white py-4 px-6 rounded-full font-semibold transition-all duration-200 hover:scale-105 flex items-center justify-center space-x-2'>
						<User className='w-5 h-5' />
						<span>Profile</span>
					</button>
				</div>
			</div>
		</div>
	);
};

export default ProfileCarousel;
