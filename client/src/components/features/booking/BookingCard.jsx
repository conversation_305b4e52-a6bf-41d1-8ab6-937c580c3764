import React from 'react';
import {
	Calendar,
	Clock,
	User,
	Mail,
	Phone,
	Trash2,
	Edit,
	MapPin,
	XCircle,
} from 'lucide-react';
import BookingStatusBadge from './BookingStatusBadge';

const BookingCard = ({
	booking,
	onEdit,
	onDelete,
	onCancel,
	showActions = true,
	isUserView = false,
	className = '',
}) => {
	const formatDate = (date) => {
		if (!date) return 'N/A';
		return new Date(date).toLocaleDateString('en-US', {
			weekday: 'short',
			year: 'numeric',
			month: 'short',
			day: 'numeric',
		});
	};

	const formatTime = (date) => {
		if (!date) return 'N/A';
		return new Date(date).toLocaleTimeString('en-US', {
			hour: '2-digit',
			minute: '2-digit',
			hour12: true,
		});
	};

	const isPastBooking = (date) => {
		return new Date(date) < new Date();
	};

	const canCancel = (booking) => {
		return booking.status === 'scheduled' && !isPastBooking(booking.date);
	};

	const getTimeUntilBooking = (date) => {
		if (!date) return '';
		const now = new Date();
		const bookingDate = new Date(date);
		const diffTime = bookingDate - now;

		if (diffTime < 0) return '';

		const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
		const diffHours = Math.ceil(diffTime / (1000 * 60 * 60));

		if (diffDays > 1) return `in ${diffDays} days`;
		if (diffHours > 1) return `in ${diffHours} hours`;
		return 'soon';
	};

	const timeUntil = getTimeUntilBooking(booking.date);
	const isUpcoming =
		booking.status === 'scheduled' && !isPastBooking(booking.date);

	return (
		<div
			className={`bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md hover:border-teal-200 transition-all duration-300 group ${className}`}>
			{/* Header */}
			<div className='flex justify-between items-start mb-5'>
				<div className='flex items-start space-x-4'>
					<div className='bg-gradient-to-br from-teal-100 to-cyan-100 p-3 rounded-xl group-hover:from-teal-200 group-hover:to-cyan-200 transition-colors duration-300'>
						<Calendar className='w-5 h-5 text-teal-600' />
					</div>
					<div>
						<h3 className='font-semibold text-gray-900 text-lg mb-1'>
							{booking.sessionType || 'Discovery Session'}
						</h3>
						<div className='flex items-center space-x-2'>
							<div className='flex items-center space-x-1 text-sm text-gray-600'>
								<Clock className='w-4 h-4' />
								<span>{formatDate(booking.date)}</span>
							</div>
							<span className='text-gray-400'>•</span>
							<span className='text-sm font-medium text-gray-700'>
								{formatTime(booking.date)}
							</span>
						</div>
						{isUpcoming && timeUntil && (
							<p className='text-xs text-teal-600 font-medium mt-1 bg-teal-50 px-2 py-0.5 rounded-full inline-block'>
								{timeUntil}
							</p>
						)}
					</div>
				</div>
				<BookingStatusBadge status={booking.status} />
			</div>

			{/* Details */}
			<div className='space-y-3 mb-5'>
				{!isUserView && booking.bookedBy && (
					<div className='flex items-center space-x-3 text-sm'>
						<div className='w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center flex-shrink-0'>
							<User className='w-4 h-4 text-gray-500' />
						</div>
						<div>
							<p className='text-gray-900 font-medium'>{booking.bookedBy}</p>
							<p className='text-gray-500 text-xs'>Client</p>
						</div>
					</div>
				)}

				{booking.email && (
					<div className='flex items-center space-x-3 text-sm'>
						<div className='w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center flex-shrink-0'>
							<Mail className='w-4 h-4 text-gray-500' />
						</div>
						<div>
							<p className='text-gray-700'>{booking.email}</p>
							<p className='text-gray-500 text-xs'>Email</p>
						</div>
					</div>
				)}

				{booking.phone && (
					<div className='flex items-center space-x-3 text-sm'>
						<div className='w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center flex-shrink-0'>
							<Phone className='w-4 h-4 text-gray-500' />
						</div>
						<div>
							<p className='text-gray-700'>{booking.phone}</p>
							<p className='text-gray-500 text-xs'>Phone</p>
						</div>
					</div>
				)}

				{booking.location && (
					<div className='flex items-center space-x-3 text-sm'>
						<div className='w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center flex-shrink-0'>
							<MapPin className='w-4 h-4 text-gray-500' />
						</div>
						<div>
							<p className='text-gray-700'>{booking.location}</p>
							<p className='text-gray-500 text-xs'>Location</p>
						</div>
					</div>
				)}
			</div>

			{/* Actions */}
			{showActions && (
				<div className='flex justify-end space-x-2 pt-4 border-t border-gray-100'>
					{isUserView ? (
						<>
							{canCancel(booking) && (
								<button
									onClick={() => onCancel?.(booking)}
									className='px-4 py-2 text-sm text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-all duration-200 flex items-center space-x-2 font-medium border border-red-200 hover:border-red-300'>
									<XCircle className='w-4 h-4' />
									<span>Cancel Booking</span>
								</button>
							)}
						</>
					) : (
						<>
							<button
								onClick={() => onEdit?.(booking)}
								disabled={
									booking.status === 'completed' ||
									booking.status === 'cancelled'
								}
								className='px-4 py-2 text-sm text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-all duration-200 flex items-center space-x-2 font-medium border border-blue-200 hover:border-blue-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-transparent disabled:hover:border-blue-200'>
								<Edit className='w-4 h-4' />
								<span>Edit</span>
							</button>
							<button
								onClick={() => onDelete?.(booking)}
								className='px-4 py-2 text-sm text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-all duration-200 flex items-center space-x-2 font-medium border border-red-200 hover:border-red-300'>
								<Trash2 className='w-4 h-4' />
								<span>Delete</span>
							</button>
						</>
					)}
				</div>
			)}

			{/* Empty state for no actions */}
			{showActions && !isUserView && booking.status !== 'scheduled' && (
				<div className='pt-4 border-t border-gray-100'>
					<p className='text-xs text-gray-500 text-center italic'>
						{booking.status === 'completed'
							? 'Session completed'
							: 'Session cancelled'}
					</p>
				</div>
			)}
		</div>
	);
};

export default BookingCard;
