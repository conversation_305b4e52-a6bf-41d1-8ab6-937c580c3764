import React, { useState, useRef, useEffect } from 'react';
import {
	MoreVertical,
	Edit,
	Trash2,
	CheckCircle,
	XCircle,
	Eye,
} from 'lucide-react';

const BookingActionMenu = ({
	booking,
	onView,
	onEdit,
	onDelete,
	onCancel,
	onMarkComplete,
	className = '',
}) => {
	const [isOpen, setIsOpen] = useState(false);
	const menuRef = useRef(null);

	useEffect(() => {
		const handleClickOutside = (event) => {
			if (menuRef.current && !menuRef.current.contains(event.target)) {
				setIsOpen(false);
			}
		};

		const handleEscape = (event) => {
			if (event.key === 'Escape') {
				setIsOpen(false);
			}
		};

		if (isOpen) {
			document.addEventListener('mousedown', handleClickOutside);
			document.addEventListener('keydown', handleEscape);
		}

		return () => {
			document.removeEventListener('mousedown', handleClickOutside);
			document.removeEventListener('keydown', handleEscape);
		};
	}, [isOpen]);

	const handleAction = (action, callback) => {
		setIsOpen(false);
		callback?.(booking);
	};

	const isPastBooking = (date) => {
		return new Date(date) < new Date();
	};

	const canCancel =
		booking.status === 'scheduled' && !isPastBooking(booking.date);
	const canComplete =
		booking.status === 'scheduled' && isPastBooking(booking.date);
	const isCompleted = booking.status === 'completed';
	const isCancelled = booking.status === 'cancelled';

	return (
		<div
			className={`relative ${className}`}
			ref={menuRef}>
			<button
				onClick={() => setIsOpen(!isOpen)}
				className='p-2 rounded-full hover:bg-gray-100 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-1'
				aria-label='More actions'
				aria-haspopup='true'
				aria-expanded={isOpen}>
				<MoreVertical className='w-4 h-4 text-gray-500' />
			</button>

			{isOpen && (
				<>
					{/* Backdrop overlay for mobile */}
					<div
						className='fixed inset-0 z-40 md:hidden'
						onClick={() => setIsOpen(false)}
					/>

					<div className='absolute right-0 top-10 w-48 bg-white rounded-lg shadow-xl border border-gray-200 py-2 z-50 animate-in slide-in-from-top-2 duration-200'>
						{onView && (
							<button
								onClick={() => handleAction('view', onView)}
								className='w-full px-4 py-2.5 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-3 transition-colors duration-150 focus:outline-none focus:bg-gray-50'>
								<Eye className='w-4 h-4 text-gray-400' />
								<span className='font-medium'>View Details</span>
							</button>
						)}

						{canComplete && onMarkComplete && (
							<button
								onClick={() => handleAction('complete', onMarkComplete)}
								className='w-full px-4 py-2.5 text-left text-sm text-green-700 hover:bg-green-50 flex items-center space-x-3 transition-colors duration-150 focus:outline-none focus:bg-green-50'>
								<CheckCircle className='w-4 h-4 text-green-500' />
								<span className='font-medium'>Mark Complete</span>
							</button>
						)}

						{canCancel && onCancel && (
							<button
								onClick={() => handleAction('cancel', onCancel)}
								className='w-full px-4 py-2.5 text-left text-sm text-orange-700 hover:bg-orange-50 flex items-center space-x-3 transition-colors duration-150 focus:outline-none focus:bg-orange-50'>
								<XCircle className='w-4 h-4 text-orange-500' />
								<span className='font-medium'>Cancel Booking</span>
							</button>
						)}

						{/* Separator before delete */}
						{onDelete && (onView || onEdit || canComplete || canCancel) && (
							<div className='border-t border-gray-100 my-1' />
						)}

						{onDelete && (
							<button
								onClick={() => handleAction('delete', onDelete)}
								className='w-full px-4 py-2.5 text-left text-sm text-red-700 hover:bg-red-50 flex items-center space-x-3 transition-colors duration-150 focus:outline-none focus:bg-red-50'>
								<Trash2 className='w-4 h-4 text-red-500' />
								<span className='font-medium'>Delete Booking</span>
							</button>
						)}

						{/* Show status info if no actions available */}
						{!onView && !onEdit && !canComplete && !canCancel && !onDelete && (
							<div className='px-4 py-2.5 text-sm text-gray-500 italic'>
								No actions available
							</div>
						)}
					</div>
				</>
			)}
		</div>
	);
};

export default BookingActionMenu;
