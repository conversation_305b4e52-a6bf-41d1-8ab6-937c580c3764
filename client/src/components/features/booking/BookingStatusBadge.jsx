import React from 'react';
import { CheckCircle, Clock, XCircle, AlertTriangle } from 'lucide-react';

const BookingStatusBadge = ({
	status,
	className = '',
	showIcon = true,
	size = 'default',
}) => {
	const getStatusConfig = (status) => {
		switch (status?.toLowerCase()) {
			case 'scheduled':
				return {
					styles: 'bg-blue-50 text-blue-700 border-blue-200 ring-blue-600/20',
					icon: Clock,
					label: 'Scheduled',
				};
			case 'completed':
				return {
					styles:
						'bg-green-50 text-green-700 border-green-200 ring-green-600/20',
					icon: CheckCircle,
					label: 'Completed',
				};
			case 'cancelled':
				return {
					styles: 'bg-red-50 text-red-700 border-red-200 ring-red-600/20',
					icon: XCircle,
					label: 'Cancelled',
				};
			case 'pending':
				return {
					styles:
						'bg-yellow-50 text-yellow-700 border-yellow-200 ring-yellow-600/20',
					icon: AlertTriangle,
					label: 'Pending',
				};
			default:
				return {
					styles: 'bg-gray-50 text-gray-700 border-gray-200 ring-gray-600/20',
					icon: Clock,
					label: 'Unknown',
				};
		}
	};

	const getSizeClasses = (size) => {
		switch (size) {
			case 'small':
				return 'px-2 py-0.5 text-xs';
			case 'large':
				return 'px-4 py-1.5 text-sm';
			default:
				return 'px-3 py-1 text-sm';
		}
	};

	const getIconSize = (size) => {
		switch (size) {
			case 'small':
				return 'w-3 h-3';
			case 'large':
				return 'w-5 h-5';
			default:
				return 'w-4 h-4';
		}
	};

	const config = getStatusConfig(status);
	const sizeClasses = getSizeClasses(size);
	const iconSizeClasses = getIconSize(size);
	const IconComponent = config.icon;

	return (
		<span
			className={`inline-flex items-center gap-1.5 rounded-full border font-medium ring-1 ring-inset transition-all duration-200 ${config.styles} ${sizeClasses} ${className}`}
			title={`Status: ${config.label}`}>
			{showIcon && <IconComponent className={iconSizeClasses} />}
			<span className='leading-none'>{config.label}</span>
		</span>
	);
};

export default BookingStatusBadge;
