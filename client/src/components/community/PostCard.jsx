import React, { useState, useCallback, useMemo } from 'react';
import {
	Bookmark,
	Heart,
	MessageSquare,
	MoreHorizontal,
	Send,
	User2Icon,
	UserCircle,
} from 'lucide-react';
import Image from 'next/image';
import toast from 'react-hot-toast';

import {
	useToggleLike as useToggleGoalLike,
	useToggleBookmark as useToggleGoalBookmark,
	useAddComment as useAddGoalComment,
} from '@/src/lib/hooks/useGoal';
import {
	useToggleLike as useToggleValueLike,
	useToggleBookmark as useToggleValueBookmark,
	useAddComment as useAddValueComment,
} from '@/src/lib/hooks/useValues';
import {
	useToggleLike as useToggleMissionLike,
	useToggleBookmark as useToggleMissionBookmark,
	useAddComment as useAddMissionComment,
} from '@/src/lib/hooks/useLifeMissions';

const PostCard = ({
	post,
	onCommentClick,
	currentUserId,
	userProfile,
	updatePostLike,
	updatePostBookmark,
	updatePostComments,
}) => {
	const [showCommentInput, setShowCommentInput] = useState(false);
	const [commentText, setCommentText] = useState('');
	const [isSubmittingComment, setIsSubmittingComment] = useState(false);

	// Memoized hooks selection based on post type
	const hooks = useMemo(() => {
		const hookMap = {
			goal: {
				useToggleLike: useToggleGoalLike,
				useToggleBookmark: useToggleGoalBookmark,
				useAddComment: useAddGoalComment,
			},
			value: {
				useToggleLike: useToggleValueLike,
				useToggleBookmark: useToggleValueBookmark,
				useAddComment: useAddValueComment,
			},
			lifeMission: {
				useToggleLike: useToggleMissionLike,
				useToggleBookmark: useToggleMissionBookmark,
				useAddComment: useAddMissionComment,
			},
		};

		return hookMap[post.type] || hookMap.goal;
	}, [post.type]);

	// Initialize mutations
	const toggleLikeMutation = hooks.useToggleLike();
	const toggleBookmarkMutation = hooks.useToggleBookmark();
	const addCommentMutation = hooks.useAddComment();

	// Memoized time formatting
	const timeAgo = useMemo(() => {
		if (!post.createdAt) return '';

		const date = new Date(post.createdAt);
		const now = new Date();
		const diffInSeconds = Math.floor((now - date) / 1000);

		if (diffInSeconds < 60) return 'just now';
		if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
		if (diffInSeconds < 86400)
			return `${Math.floor(diffInSeconds / 3600)}h ago`;
		if (diffInSeconds < 604800)
			return `${Math.floor(diffInSeconds / 86400)}d ago`;
		return date.toLocaleDateString();
	}, [post.createdAt]);

	// Optimized handlers with useCallback
	const handleLike = useCallback(async () => {
		const newLikedState = !post.isLiked;
		updatePostLike(post.id, newLikedState);

		try {
			await toggleLikeMutation.mutateAsync(post.originalId);
		} catch (error) {
			updatePostLike(post.id, post.isLiked);
			toast.error('Failed to update like');
			console.error('Error toggling like:', error);
		}
	}, [
		post.id,
		post.isLiked,
		post.originalId,
		updatePostLike,
		toggleLikeMutation,
	]);

	const handleBookmark = useCallback(async () => {
		const newBookmarkedState = !post.isBookmarked;
		updatePostBookmark(post.id, newBookmarkedState);

		try {
			await toggleBookmarkMutation.mutateAsync(post.originalId);
			toast.success(newBookmarkedState ? 'Bookmarked!' : 'Removed bookmark');
		} catch (error) {
			updatePostBookmark(post.id, post.isBookmarked);
			toast.error('Failed to update bookmark');
			console.error('Error toggling bookmark:', error);
		}
	}, [
		post.id,
		post.isBookmarked,
		post.originalId,
		updatePostBookmark,
		toggleBookmarkMutation,
	]);

	const handleCommentSubmit = useCallback(
		async (e) => {
			e.preventDefault();
			if (!commentText.trim()) return;

			setIsSubmittingComment(true);
			updatePostComments(post.id, true);

			try {
				await addCommentMutation.mutateAsync({
					id: post.originalId,
					text: commentText.trim(),
				});
				setCommentText('');
				setShowCommentInput(false);
				toast.success('Comment added!');
			} catch (error) {
				updatePostComments(post.id, false);
				toast.error('Failed to add comment');
				console.error('Error adding comment:', error);
			} finally {
				setIsSubmittingComment(false);
			}
		},
		[
			commentText,
			post.id,
			post.originalId,
			updatePostComments,
			addCommentMutation,
		],
	);

	const toggleCommentInput = useCallback(() => {
		setShowCommentInput((prev) => !prev);
	}, []);

	const handlePostClick = useCallback(() => {
		onCommentClick?.(post);
	}, [onCommentClick, post]);

	const handleCommentTextChange = useCallback((e) => {
		setCommentText(e.target.value);
	}, []);

	// Author avatar component
	const AuthorAvatar = ({ size = 12 }) => (
		<div
			className={`w-${size} h-${size} rounded-full overflow-hidden flex-shrink-0`}>
			{post.author.avatar ? (
				<Image
					src={post.author.avatar}
					alt={post.author.name}
					width={size * 4}
					height={size * 4}
					className={`rounded-full object-cover h-${size} w-${size} shadow-2xl border border-gray-400`}
				/>
			) : (
				<User2Icon className={`text-gray-700 w-${size} h-${size}`} />
			)}
		</div>
	);

	// User avatar component
	const UserAvatar = ({ size = 8 }) => (
		<div className={`w-${size} h-${size} rounded-full flex-shrink-0`}>
			{userProfile?.avatar ? (
				<Image
					src={userProfile.avatar}
					alt='You'
					width={size * 4}
					height={size * 4}
					className={`rounded-full object-cover h-${size} w-${size} shadow-2xl border border-gray-400`}
				/>
			) : (
				<UserCircle className={`text-gray-700 w-${size} h-${size}`} />
			)}
		</div>
	);

	return (
		<div className='bg-white border-b border-gray-200 py-4 mb-4 rounded-md shadow-sm'>
			{/* Header */}
			<div className='flex items-start space-x-3 px-4 mb-3'>
				<AuthorAvatar />
				<div className='flex-1'>
					<div className='flex items-center justify-between'>
						<div>
							<span className='font-semibold text-black'>
								{post.author.name}
							</span>
							<span className='text-gray-500 text-sm ml-2'>
								shared {post.postTypeLabel}
							</span>
							{timeAgo && (
								<span className='text-gray-400 text-sm ml-2'>• {timeAgo}</span>
							)}
						</div>
					</div>
				</div>
			</div>

			{/* Content */}
			<div className='px-4 mb-4'>
				<div
					className='bg-teal-50 border-2 border-teal-200 rounded-xl p-4 cursor-pointer hover:bg-teal-100 transition-colors'
					onClick={handlePostClick}>
					<p className='text-gray-800 font-medium mb-2'>{post.content}</p>
					{post.description && (
						<p className='text-gray-600 text-sm'>{post.description}</p>
					)}
				</div>
			</div>

			{/* Actions */}
			<div className='flex items-center justify-between px-4'>
				<div className='flex items-center space-x-6'>
					<button
						onClick={handleLike}
						disabled={toggleLikeMutation.isLoading}
						className={`flex items-center space-x-2 transition-colors disabled:opacity-50 ${
							post.isLiked ? 'text-red-500' : 'text-gray-600 hover:text-red-500'
						}`}>
						<Heart
							className={`w-5 h-5 ${post.isLiked ? 'fill-current' : ''}`}
						/>
						<span className='text-sm'>{post.likes}</span>
					</button>

					<button
						onClick={toggleCommentInput}
						className='flex items-center space-x-2 text-gray-600 hover:text-blue-500 transition-colors'>
						<MessageSquare className='w-5 h-5' />
						<span className='text-sm'>
							{post.comments || post.commentData?.length || 0}
						</span>
					</button>
				</div>

				<button
					onClick={handleBookmark}
					disabled={toggleBookmarkMutation.isLoading}
					className={`transition-colors disabled:opacity-50 ${
						post.isBookmarked
							? 'text-yellow-500'
							: 'text-gray-600 hover:text-yellow-500'
					}`}>
					<Bookmark
						className={`w-5 h-5 ${post.isBookmarked ? 'fill-current' : ''}`}
					/>
				</button>
			</div>

			{/* Comment Input */}
			{showCommentInput && (
				<div className='px-4 mt-4 pt-4 border-t border-gray-100'>
					<form
						onSubmit={handleCommentSubmit}
						className='flex space-x-3'>
						<UserAvatar />
						<div className='flex-1 flex space-x-2'>
							<input
								type='text'
								value={commentText}
								onChange={handleCommentTextChange}
								placeholder='Write a comment...'
								className='flex-1 px-3 py-2 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent text-black'
								disabled={isSubmittingComment}
							/>
							<button
								type='submit'
								disabled={!commentText.trim() || isSubmittingComment}
								className='px-4 py-2 bg-teal-600 text-white rounded-full hover:bg-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors'>
								{isSubmittingComment ? (
									<div className='w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin' />
								) : (
									<Send className='w-4 h-4' />
								)}
							</button>
						</div>
					</form>
				</div>
			)}
		</div>
	);
};

export default PostCard;
