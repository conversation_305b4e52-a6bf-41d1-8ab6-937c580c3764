import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { X, Send, Trash2, MessageSquare } from 'lucide-react';
import Image from 'next/image';
import toast from 'react-hot-toast';

import { useUserProfile } from '@/src/lib/hooks/useUser';
import { useGoalById } from '@/src/lib/hooks/useGoal';
import { useValueById } from '@/src/lib/hooks/useValues';
import { useLifeMissionById } from '@/src/lib/hooks/useLifeMissions';
import {
	useAddComment as useAddGoalComment,
	useDeleteComment as useDeleteGoalComment,
} from '@/src/lib/hooks/useGoal';
import {
	useAddComment as useAddValueComment,
	useDeleteComment as useDeleteValueComment,
} from '@/src/lib/hooks/useValues';
import {
	useAddComment as useAddMissionComment,
	useDeleteComment as useDeleteMissionComment,
} from '@/src/lib/hooks/useLifeMissions';

const CommentModal = ({ post, isOpen, onClose }) => {
	const [commentText, setCommentText] = useState('');
	const [isSubmittingComment, setIsSubmittingComment] = useState(false);

	const { data: userProfile } = useUserProfile();
	const currentUser = useMemo(
		() =>
			userProfile?.data?.user ||
			userProfile?.data ||
			userProfile?.user ||
			userProfile,
		[userProfile],
	);

	// Validate post data
	const isValidPost = useMemo(
		() => post && post.originalId && post.type,
		[post],
	);

	const shouldFetchData = isOpen && isValidPost;

	// Conditionally fetch detail data based on post type
	const goalDetail = useGoalById(
		shouldFetchData && post.type === 'goal' ? post.originalId : null,
	);
	const valueDetail = useValueById(
		shouldFetchData && post.type === 'value' ? post.originalId : null,
	);
	const missionDetail = useLifeMissionById(
		shouldFetchData && post.type === 'lifeMission' ? post.originalId : null,
	);

	// Initialize all hooks at the top level
	const goalAddComment = useAddGoalComment();
	const goalDeleteComment = useDeleteGoalComment();
	const valueAddComment = useAddValueComment();
	const valueDeleteComment = useDeleteValueComment();
	const missionAddComment = useAddMissionComment();
	const missionDeleteComment = useDeleteMissionComment();

	// Memoized data extraction
	const { detailData, isLoadingDetail, hasError } = useMemo(() => {
		if (!isValidPost) {
			return { detailData: null, isLoadingDetail: false, hasError: false };
		}

		switch (post.type) {
			case 'goal':
				return {
					detailData: goalDetail.data,
					isLoadingDetail: goalDetail.isLoading,
					hasError: goalDetail.error,
				};
			case 'value':
				return {
					detailData: valueDetail.data,
					isLoadingDetail: valueDetail.isLoading,
					hasError: valueDetail.error,
				};
			case 'lifeMission':
				return {
					detailData: missionDetail.data,
					isLoadingDetail: missionDetail.isLoading,
					hasError: missionDetail.error,
				};
			default:
				return { detailData: null, isLoadingDetail: false, hasError: false };
		}
	}, [
		post?.type,
		goalDetail.data,
		goalDetail.isLoading,
		goalDetail.error,
		valueDetail.data,
		valueDetail.isLoading,
		valueDetail.error,
		missionDetail.data,
		missionDetail.isLoading,
		missionDetail.error,
		isValidPost,
	]);

	// Extract comments from detail data
	const comments = useMemo(() => {
		return detailData?.data?.comments || [];
	}, [detailData]);

	// Select appropriate hooks based on post type
	const commentHooks = useMemo(() => {
		if (!isValidPost) return { add: goalAddComment, del: goalDeleteComment };

		const hookMap = {
			goal: { add: goalAddComment, del: goalDeleteComment },
			value: { add: valueAddComment, del: valueDeleteComment },
			lifeMission: { add: missionAddComment, del: missionDeleteComment },
		};

		return hookMap[post.type] || hookMap.goal;
	}, [
		post?.type,
		goalAddComment,
		goalDeleteComment,
		valueAddComment,
		valueDeleteComment,
		missionAddComment,
		missionDeleteComment,
		isValidPost,
	]);

	// Memoized time formatting function
	const formatTimeAgo = useCallback((dateString) => {
		const date = new Date(dateString);
		const now = new Date();
		const seconds = Math.floor((now - date) / 1000);

		if (seconds < 60) return 'just now';
		if (seconds < 3600) return `${Math.floor(seconds / 60)}m ago`;
		if (seconds < 86400) return `${Math.floor(seconds / 3600)}h ago`;
		if (seconds < 604800) return `${Math.floor(seconds / 86400)}d ago`;
		return date.toLocaleDateString();
	}, []);

	// Optimized handlers
	const handleCommentSubmit = useCallback(
		async (e) => {
			e.preventDefault();
			if (!commentText.trim() || !isValidPost) return;

			setIsSubmittingComment(true);
			try {
				await commentHooks.add.mutateAsync({
					id: post.originalId,
					text: commentText.trim(),
				});
				setCommentText('');
				toast.success('Comment added!');
			} catch (err) {
				console.error('Error adding comment:', err);
				toast.error('Failed to add comment');
			} finally {
				setIsSubmittingComment(false);
			}
		},
		[commentText, post?.originalId, commentHooks.add, isValidPost],
	);

	const handleDeleteComment = useCallback(
		async (commentId) => {
			if (
				!window.confirm('Are you sure you want to delete this comment?') ||
				!isValidPost
			) {
				return;
			}

			try {
				await commentHooks.del.mutateAsync({
					id: post.originalId,
					commentId,
				});
				toast.success('Comment deleted!');
			} catch (err) {
				console.error('Error deleting comment:', err);
				toast.error('Failed to delete comment');
			}
		},
		[post?.originalId, commentHooks.del, isValidPost],
	);

	const handleCommentTextChange = useCallback((e) => {
		setCommentText(e.target.value);
	}, []);

	const handleKeyDown = useCallback(
		(e) => {
			if (e.key === 'Enter' && !e.shiftKey) {
				e.preventDefault();
				handleCommentSubmit(e);
			}
		},
		[handleCommentSubmit],
	);

	// Escape key handler
	useEffect(() => {
		const handleEscape = (e) => {
			if (e.key === 'Escape') onClose();
		};

		if (isOpen) {
			document.addEventListener('keydown', handleEscape);
			document.body.style.overflow = 'hidden';
		}

		return () => {
			document.removeEventListener('keydown', handleEscape);
			document.body.style.overflow = 'unset';
		};
	}, [isOpen, onClose]);

	// Early returns for invalid states
	if (!isOpen || !post) return null;

	if (!isValidPost) {
		return (
			<div className='fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50 p-4'>
				<div className='bg-white rounded-2xl max-w-md w-full p-6 text-center'>
					<div className='w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4'>
						<X className='w-8 h-8 text-red-500' />
					</div>
					<h3 className='text-lg font-semibold text-gray-900 mb-2'>
						Invalid Post Data
					</h3>
					<p className='text-gray-600 mb-4'>
						The post data is incomplete or invalid. Please try again.
					</p>
					<button
						onClick={onClose}
						className='px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors'>
						Close
					</button>
				</div>
			</div>
		);
	}

	// Avatar component
	const Avatar = ({ src, name, size = 'w-10 h-10' }) => (
		<div
			className={`${size} bg-gradient-to-br from-blue-400 to-purple-500 rounded-full overflow-hidden flex-shrink-0 ring-2 ring-white shadow-sm`}>
			<Image
				src={
					src ||
					`https://ui-avatars.com/api/?name=${encodeURIComponent(
						name || 'Anonymous',
					)}&background=random`
				}
				alt={name || 'Anonymous'}
				width={40}
				height={40}
				className='w-full h-full object-cover'
			/>
		</div>
	);

	// Error state component
	const ErrorState = () => (
		<div className='flex flex-col items-center justify-center py-16 text-center'>
			<div className='w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4'>
				<X className='w-8 h-8 text-red-500' />
			</div>
			<h3 className='text-lg font-semibold text-gray-900 mb-2'>
				Error Loading Comments
			</h3>
			<p className='text-gray-500 max-w-sm leading-relaxed mb-4'>
				We couldn't load the comments for this post. Please try again later.
			</p>
			<button
				onClick={onClose}
				className='px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors'>
				Close
			</button>
		</div>
	);

	// Loading state component
	const LoadingState = () => (
		<div className='flex items-center justify-center py-12'>
			<div className='flex items-center space-x-3'>
				<div className='w-6 h-6 border-2 border-teal-600 border-t-transparent rounded-full animate-spin' />
				<span className='text-gray-600 font-medium'>Loading comments...</span>
			</div>
		</div>
	);

	// Empty state component
	const EmptyState = () => (
		<div className='flex flex-col items-center justify-center py-16 text-center'>
			<div className='w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4'>
				<MessageSquare className='w-8 h-8 text-gray-400' />
			</div>
			<h3 className='text-lg font-semibold text-gray-900 mb-2'>
				No comments yet
			</h3>
			<p className='text-gray-500 max-w-sm leading-relaxed'>
				Be the first to share your thoughts and start a meaningful conversation!
			</p>
		</div>
	);

	return (
		<div className='fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50 p-4'>
			<div className='bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-hidden flex flex-col shadow-2xl'>
				{/* Header */}
				<div className='flex items-center justify-between p-6 border-b border-gray-200'>
					<div className='flex items-center space-x-3'>
						<MessageSquare className='w-6 h-6 text-teal-600' />
						<h2 className='text-xl font-semibold text-gray-900'>Comments</h2>
						<span className='bg-gray-100 text-gray-600 text-sm px-2 py-1 rounded-full font-medium'>
							{post.comments || post.commentData?.length || 0}
						</span>
					</div>
					<button
						onClick={onClose}
						className='text-gray-400 hover:text-gray-600 p-2 rounded-full hover:bg-gray-100 transition-colors'>
						<X className='w-6 h-6' />
					</button>
				</div>

				{/* Post Preview */}
				<div className='p-6 border-b border-gray-100 bg-gray-50'>
					<div className='flex items-start space-x-4'>
						<Avatar
							src={post.author.avatar}
							name={post.author.name}
							size='w-12 h-12'
						/>
						<div className='flex-1 min-w-0'>
							<div className='flex items-center space-x-2 mb-2'>
								<span className='font-semibold text-gray-900'>
									{post.author.name}
								</span>
								<span className='text-gray-500 text-sm'>
									shared {post.postTypeLabel}
								</span>
							</div>
							<div className='bg-teal-50 border border-teal-200 rounded-lg p-3'>
								<p className='text-gray-900 font-medium mb-1'>{post.content}</p>
								{post.description && (
									<p className='text-gray-700 text-sm'>{post.description}</p>
								)}
							</div>
						</div>
					</div>
				</div>

				{/* Comments List */}
				<div className='flex-1 overflow-y-auto'>
					{hasError ? (
						<ErrorState />
					) : isLoadingDetail ? (
						<LoadingState />
					) : comments.length > 0 ? (
						<div className='p-6 space-y-6'>
							{comments.map((comment, index) => (
								<div
									key={comment._id}
									className={`flex items-start space-x-4 ${
										index !== comments.length - 1
											? 'pb-6 border-b border-gray-100'
											: ''
									}`}>
									<Avatar
										src={comment.userAvatar}
										name={comment.userName}
									/>
									<div className='flex-1 min-w-0'>
										<div className='bg-gray-100 rounded-2xl px-4 py-3'>
											<div className='flex items-center justify-between mb-2'>
												<span className='font-semibold text-sm text-gray-900'>
													{comment.userName || 'Anonymous'}
												</span>
												{comment.userId === currentUser?._id && (
													<button
														onClick={() => handleDeleteComment(comment._id)}
														className='text-gray-400 hover:text-red-500 p-1 rounded-full hover:bg-red-50 transition-colors'
														disabled={commentHooks.del.isLoading}
														title='Delete comment'>
														<Trash2 className='w-4 h-4' />
													</button>
												)}
											</div>
											<p className='text-gray-800 leading-relaxed'>
												{comment.text}
											</p>
										</div>
										<div className='flex items-center space-x-4 mt-2 ml-4'>
											<time className='text-xs text-gray-500 font-medium'>
												{formatTimeAgo(comment.createdAt)}
											</time>
										</div>
									</div>
								</div>
							))}
						</div>
					) : (
						<EmptyState />
					)}
				</div>

				{/* Comment Input */}
				{!hasError && (
					<div className='p-6 border-t border-gray-200 bg-gray-50'>
						<form
							onSubmit={handleCommentSubmit}
							className='flex items-start space-x-4'>
							<Avatar
								src={currentUser?.avatar}
								name={currentUser?.name}
							/>
							<div className='flex-1 flex flex-col space-y-3'>
								<div className='flex items-end space-x-3'>
									<div className='flex-1'>
										<textarea
											value={commentText}
											onChange={handleCommentTextChange}
											onKeyDown={handleKeyDown}
											placeholder='Write a comment...'
											rows={3}
											className='w-full text-black px-4 py-3 border border-gray-300 rounded-xl resize-none focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all duration-200'
											disabled={isSubmittingComment}
										/>
									</div>
									<button
										type='submit'
										disabled={!commentText.trim() || isSubmittingComment}
										className='px-6 py-3 bg-teal-600 text-white rounded-xl hover:bg-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium shadow-sm hover:shadow-md'>
										{isSubmittingComment ? (
											<div className='w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin' />
										) : (
											<Send className='w-5 h-5' />
										)}
									</button>
								</div>
								<p className='text-xs text-gray-500 ml-1'>
									Press Enter to post, Shift+Enter for new line
								</p>
							</div>
						</form>
					</div>
				)}
			</div>
		</div>
	);
};

export default CommentModal;
