'use client';
import React, { useRef, useState, useEffect } from 'react';
import Link from 'next/link';
import 'swiper/css'; // Import Swiper styles
import { useLessons, useUserProfile } from '@/src/lib/hooks';
import Sidebar from '../layout/Sidebar';
import Header from '../layout/Header';
import OngoingVideosSection from '../features/lessons/OngoingVideosSection';
import CourseCard from '../features/lessons/CourseCard';
import { MdError } from 'react-icons/md';
import List from './List';

const HomePage = () => {
	// Fetch user profile data
	const {
		data: userProfile,
		isLoading: isUserLoading,
		error: userError,
	} = useUserProfile();

	const [isSidebarOpen, setIsSidebarOpen] = useState(false);
	const mobileSwiperRef = useRef(null);
	const desktopSwiperRef = useRef(null);
	const [windowWidth, setWindowWidth] = useState(
		typeof window !== 'undefined' ? window.innerWidth : 0,
	);
	const [slidesPerView, setSlidesPerView] = useState(1);

	// Handle window resize for responsive design
	useEffect(() => {
		const handleResize = () => {
			setWindowWidth(window.innerWidth);

			// Adjust slides per view based on screen width
			if (window.innerWidth >= 1280) {
				setSlidesPerView(3); // XL screens
			} else if (window.innerWidth >= 1024) {
				setSlidesPerView(2.5); // Large screens
			} else if (window.innerWidth >= 768) {
				setSlidesPerView(2); // Medium screens
			} else {
				setSlidesPerView(1); // Small screens
			}
		};

		// Set initial value
		handleResize();

		// Add event listener
		window.addEventListener('resize', handleResize);

		// Clean up
		return () => window.removeEventListener('resize', handleResize);
	}, []);

	// Set sidebar to open by default on desktop
	useEffect(() => {
		if (windowWidth >= 1024) {
			setIsSidebarOpen(false);
		}
	}, [windowWidth]);

	const toggleSidebar = () => {
		setIsSidebarOpen(!isSidebarOpen);
	};

	const {
		data: coursedata,
		isLoading,
		error,
		isFetching,
	} = useLessons({
		page: 1,
		limit: 4,
		category: '',
		title: '',
	});

	const handlePrev = () => {
		// Check if we're on mobile or desktop and use the appropriate ref
		if (windowWidth < 1024) {
			if (mobileSwiperRef.current && mobileSwiperRef.current.swiper) {
				mobileSwiperRef.current.swiper.slidePrev();
			}
		} else {
			if (desktopSwiperRef.current && desktopSwiperRef.current.swiper) {
				desktopSwiperRef.current.swiper.slidePrev();
			}
		}
	};

	const handleNext = () => {
		// Check if we're on mobile or desktop and use the appropriate ref
		if (windowWidth < 1024) {
			if (mobileSwiperRef.current && mobileSwiperRef.current.swiper) {
				mobileSwiperRef.current.swiper.slideNext();
			}
		} else {
			if (desktopSwiperRef.current && desktopSwiperRef.current.swiper) {
				desktopSwiperRef.current.swiper.slideNext();
			}
		}
	};
	console.log('Course', coursedata);

	return (
		<div className='min-h-screen flex'>
			{/* Sidebar Component */}
			<Sidebar
				isOpen={isSidebarOpen}
				onToggle={toggleSidebar}
			/>

			{/* Main Content */}
			<div
				className={`flex-1 transition-all duration-300 ${
					isSidebarOpen && windowWidth >= 1024 ? 'lg:ml-64' : 'ml-0'
				}`}>
				{/* Header Section */}
				<Header
					onMenuToggle={toggleSidebar}
					userProfile={userProfile}
					isUserLoading={isUserLoading}
				/>

				{/* Mobile Content - Only visible on mobile */}
				<div className='lg:hidden'>
					{/* Ongoing Videos Section */}
					<OngoingVideosSection isMobile={true} />

					{/* Course Cards Section */}
					<section className='px-4 pb-6 mt-8'>
						<div className='mb-6'>
							<h3 className='text-xl font-bold text-gray-900 dark:text-white mb-2'>
								Popular Courses
							</h3>
							<p className='text-gray-600 dark:text-gray-400 text-sm'>
								Explore our most popular learning content
							</p>
						</div>

						{coursedata?.data && (
							<div className='grid grid-cols-2 gap-4'>
								{coursedata?.data?.map((course, index) => (
									<CourseCard
										key={index}
										title={course.title}
										imageSrc={course.thumbnail}
										course={course}
									/>
								))}
							</div>
						)}

						{isLoading && (
							<div className='flex flex-col items-center justify-center py-16'>
								<div className='animate-spin rounded-full h-12 w-12 border-4 border-teal-600 border-t-transparent mb-4'></div>
								<p className='text-gray-600 dark:text-gray-400 text-sm'>
									Loading courses...
								</p>
							</div>
						)}

						{error && (
							<div className='flex flex-col items-center justify-center py-16 text-center'>
								<div className='bg-red-50 dark:bg-red-900/20 rounded-full p-4 mb-4'>
									<MdError className='w-8 h-8 text-red-600 dark:text-red-400' />
								</div>
								<h3 className='text-lg font-semibold text-gray-900 dark:text-white mb-2'>
									Something went wrong
								</h3>
								<p className='text-gray-600 dark:text-gray-400 text-sm'>
									Please try refreshing the page
								</p>
							</div>
						)}
					</section>

					<List />
				</div>

				{/* Desktop Content - Only visible on desktop */}
				<div className='hidden lg:block max-w-7xl mx-auto px-8 py-8'>
					{/* Ongoing Videos Section */}
					<OngoingVideosSection isMobile={false} />

					{/* Course Cards Section */}
					<section className='mb-12'>
						<div className='flex justify-between items-center mb-8'>
							<div>
								<h3 className='text-2xl font-bold text-gray-900 dark:text-white mb-2'>
									Popular Courses
								</h3>
								<p className='text-gray-600 dark:text-gray-400'>
									Discover our most popular learning content and expand your
									skills
								</p>
							</div>
							<Link
								href='/lessons'
								className='text-teal-600 dark:text-teal-400 font-semibold hover:text-teal-700 dark:hover:text-teal-300 transition-colors duration-200 flex items-center gap-2 bg-teal-50 dark:bg-teal-900/20 px-4 py-2 rounded-full'>
								View All Courses
							</Link>
						</div>

						{coursedata?.data?.data && (
							<div className='grid grid-cols-4 gap-6'>
								{coursedata?.data?.data?.map((course, index) => (
									<CourseCard
										key={index}
										title={course.title}
										imageSrc={course.thumbnail}
										course={course}
									/>
								))}
							</div>
						)}

						{isLoading && (
							<div className='flex flex-col items-center justify-center py-20'>
								<div className='animate-spin rounded-full h-16 w-16 border-4 border-teal-600 border-t-transparent mb-6'></div>
								<p className='text-gray-600 dark:text-gray-400'>
									Loading courses...
								</p>
							</div>
						)}

						{error && (
							<div className='flex flex-col items-center justify-center py-20 text-center'>
								<div className='bg-red-50 dark:bg-red-900/20 rounded-full p-6 mb-6'>
									<MdError className='w-12 h-12 text-red-600 dark:text-red-400' />
								</div>
								<h3 className='text-xl font-semibold text-gray-900 dark:text-white mb-3'>
									Something went wrong
								</h3>
								<p className='text-gray-600 dark:text-gray-400 max-w-md'>
									We're having trouble loading the courses. Please try
									refreshing the page or check your internet connection.
								</p>
							</div>
						)}
					</section>

					<List />
				</div>
			</div>
		</div>
	);
};

export default HomePage;
