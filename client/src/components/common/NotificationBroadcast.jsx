'use client';
import { useState } from 'react';
import {
	FaBell,
	FaUsers,
	FaCheckCircle,
	FaExclamationTriangle,
} from 'react-icons/fa';
import toast from 'react-hot-toast';
import { useBroadcastNotification } from '@/src/lib/hooks';

export default function NotificationBroadcast() {
	const [notificationData, setNotificationData] = useState({
		title: '',
		message: '',
	});
	const [sending, setSending] = useState(false);
	const [lastSent, setLastSent] = useState(null);

	console.log(lastSent);

	const broadcastMutation = useBroadcastNotification();

	const handleInputChange = (e) => {
		const { name, value } = e.target;
		setNotificationData((prev) => ({
			...prev,
			[name]: value,
		}));
	};

	const handleBroadcast = async () => {
		if (!notificationData.title.trim() || !notificationData.message.trim()) {
			toast.error('Please provide both title and message');
			return;
		}

		const confirmed = window.confirm(
			'Are you sure you want to send this notification to ALL users? This action cannot be undone.',
		);

		if (!confirmed) return;

		setSending(true);
		try {
			const response = await broadcastMutation.mutateAsync({
				title: notificationData.title.trim(),
				messageContent: notificationData.message.trim(),
			});

			// Update last sent info
			setLastSent({
				timestamp: new Date(),
				stats: response?.stats || {},
				title: notificationData.title,
			});

			toast.success('Notification broadcasted successfully!');

			// Clear form
			setNotificationData({ title: '', message: '' });
		} catch (error) {
			console.error('Broadcast error:', error);
			toast.error(
				error?.response?.data?.error || 'Failed to broadcast notification',
			);
		} finally {
			setSending(false);
		}
	};

	const handleTestNotification = () => {
		if (!notificationData.title.trim() || !notificationData.message.trim()) {
			toast.error('Please provide both title and message');
			return;
		}

		// Show local notification for testing
		if ('Notification' in window && Notification.permission === 'granted') {
			new Notification(notificationData.title, {
				body: notificationData.message,
				icon: '/icon.png',
				badge: '/icon.png',
			});
			toast.success('Test notification sent to your browser!');
		} else {
			toast.error(
				'Browser notifications not enabled. Please enable them to test.',
			);
		}
	};

	return (
		<div className='bg-white p-6 rounded-lg shadow-md text-black'>
			<div className='flex items-center gap-3 mb-6'>
				<div className='p-2 bg-blue-100 rounded-lg'>
					<FaBell className='text-blue-600 text-xl' />
				</div>
				<div>
					<h2 className='text-xl font-semibold text-gray-900'>
						Broadcast Notifications
					</h2>
					<p className='text-sm text-gray-600'>
						Send notifications to all subscribed users
					</p>
				</div>
			</div>

			{/* Last broadcast info */}
			{lastSent && (
				<div className='bg-green-50 border border-green-200 rounded-lg p-4 mb-6'>
					<div className='flex items-center gap-2 text-green-800 mb-2'>
						<FaCheckCircle />
						<span className='font-medium'>Last Broadcast Sent</span>
					</div>
					<p className='text-green-700 text-sm mb-1'>
						<strong>"{lastSent.title}"</strong>
					</p>
					<p className='text-green-600 text-xs mb-2'>
						{lastSent.timestamp.toLocaleString()}
					</p>
					{lastSent.stats && (
						<div className='grid grid-cols-2 md:grid-cols-4 gap-2 text-xs'>
							<div className='bg-white bg-opacity-50 p-2 rounded'>
								<div className='font-medium text-green-800'>Users Notified</div>
								<div className='text-green-700'>
									{lastSent.stats.usersNotified || 0}
								</div>
							</div>
							<div className='bg-white bg-opacity-50 p-2 rounded'>
								<div className='font-medium text-green-800'>Successful</div>
								<div className='text-green-700'>
									{lastSent.stats.valid || 0}
								</div>
							</div>
							<div className='bg-white bg-opacity-50 p-2 rounded'>
								<div className='font-medium text-green-800'>Fixed</div>
								<div className='text-green-700'>
									{lastSent.stats.fixed || 0}
								</div>
							</div>
							<div className='bg-white bg-opacity-50 p-2 rounded'>
								<div className='font-medium text-green-800'>Failed</div>
								<div className='text-green-700'>
									{lastSent.stats.invalid || 0}
								</div>
							</div>
						</div>
					)}
				</div>
			)}

			<div className='space-y-4'>
				{/* Title Input */}
				<div>
					<label
						htmlFor='title'
						className='block text-sm font-medium text-gray-700 mb-2'>
						Notification Title *
					</label>
					<input
						type='text'
						id='title'
						name='title'
						value={notificationData.title}
						onChange={handleInputChange}
						disabled={sending}
						maxLength={50}
						className='w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed'
						placeholder='Enter notification title (max 50 characters)'
					/>
					<p className='text-xs text-gray-500 mt-1'>
						{notificationData.title.length}/50 characters
					</p>
				</div>

				{/* Message Input */}
				<div>
					<label
						htmlFor='message'
						className='block text-sm font-medium text-gray-700 mb-2'>
						Notification Message *
					</label>
					<textarea
						id='message'
						name='message'
						value={notificationData.message}
						onChange={handleInputChange}
						disabled={sending}
						maxLength={200}
						rows={4}
						className='w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed resize-none'
						placeholder='Enter notification message (max 200 characters)'
					/>
					<p className='text-xs text-gray-500 mt-1'>
						{notificationData.message.length}/200 characters
					</p>
				</div>

				{/* Preview */}
				{(notificationData.title || notificationData.message) && (
					<div className='bg-gray-50 border border-gray-200 rounded-lg p-4'>
						<h4 className='text-sm font-medium text-gray-700 mb-2'>Preview:</h4>
						<div className='bg-white border border-gray-300 rounded-lg p-3 shadow-sm'>
							<div className='flex items-start gap-3'>
								<div className='flex-shrink-0'>
									<div className='w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center'>
										<FaBell className='text-blue-600 text-sm' />
									</div>
								</div>
								<div className='flex-1 min-w-0'>
									<p className='text-sm font-medium text-gray-900'>
										{notificationData.title || 'Notification Title'}
									</p>
									<p className='text-sm text-gray-600 mt-1'>
										{notificationData.message ||
											'Notification message will appear here...'}
									</p>
									<p className='text-xs text-gray-400 mt-1'>YouLife • now</p>
								</div>
							</div>
						</div>
					</div>
				)}

				{/* Action Buttons */}
				<div className='flex flex-col sm:flex-row gap-3 pt-4'>
					{/* <button
            onClick={handleTestNotification}
            disabled={sending || !notificationData.title.trim() || !notificationData.message.trim()}
            className="flex items-center justify-center gap-2 px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <FaCheckCircle />
            Test Notification
          </button> */}

					<button
						onClick={handleBroadcast}
						disabled={
							sending ||
							!notificationData.title.trim() ||
							!notificationData.message.trim()
						}
						className='flex items-center justify-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-[1.02] disabled:hover:scale-100'>
						{sending ? (
							<>
								<div className='animate-spin rounded-full h-4 w-4 border-b-2 border-white'></div>
								Sending...
							</>
						) : (
							<>
								<FaUsers />
								Broadcast to All Users
							</>
						)}
					</button>
				</div>

				{/* Warning */}
				<div className='bg-amber-50 border border-amber-200 rounded-lg p-4 mt-4'>
					<div className='flex items-start gap-2'>
						<FaExclamationTriangle className='text-amber-600 mt-0.5 flex-shrink-0' />
						<div className='text-sm text-amber-800'>
							<p className='font-medium mb-1'>Important Notice:</p>
							<p>
								This will send a push notification to all users who have
								subscribed to notifications. Make sure your message is clear and
								relevant to all users. This action cannot be undone.
							</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
