import React, { useState } from 'react';
import toast from 'react-hot-toast';

const AddValueModal = ({ onClose, onSave }) => {
	const [valueName, setValueName] = useState('');
	const [quote, setQuote] = useState('');
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [isPublic, setIsPublic] = useState(false);

	const handleSave = async () => {
		const trimmedName = valueName.trim();
		const trimmedQuote = quote.trim();

		if (!trimmedName || !trimmedQuote) {
			toast.error('Both fields are required.');
			return;
		}

		setIsSubmitting(true);
		try {
			await onSave({ name: trimmedName, definition: trimmedQuote, isPublic });
			setValueName('');
			setQuote('');
			toast.success('Value added successfully!');
		} catch (error) {
			toast.error('Failed to save value. Please try again.');
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleKeyPress = (e) => {
		if (e.key === 'Enter' && e.ctrlKey) {
			handleSave();
		}
	};

	return (
		<div className='fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4'>
			<div className='bg-white dark:bg-gray-800 rounded-2xl w-full max-w-lg shadow-2xl transform transition-all duration-300 animate-in fade-in-0 scale-in-95'>
				{/* Header */}
				<div className='flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700'>
					<div className='flex items-center gap-3'>
						<div className='w-10 h-10 bg-gradient-to-br from-teal-500 to-teal-600 rounded-full flex items-center justify-center'>
							<svg
								className='w-5 h-5 text-white'
								fill='none'
								stroke='currentColor'
								viewBox='0 0 24 24'>
								<path
									strokeLinecap='round'
									strokeLinejoin='round'
									strokeWidth={2}
									d='M12 6v6m0 0v6m0-6h6m-6 0H6'
								/>
							</svg>
						</div>
						<h2 className='text-xl font-bold text-gray-900 dark:text-white'>
							Add New Value
						</h2>
					</div>
					<button
						onClick={onClose}
						className='w-8 h-8 rounded-full bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 flex items-center justify-center transition-colors'
						disabled={isSubmitting}>
						<svg
							className='w-4 h-4 text-gray-600 dark:text-gray-400'
							fill='none'
							stroke='currentColor'
							viewBox='0 0 24 24'>
							<path
								strokeLinecap='round'
								strokeLinejoin='round'
								strokeWidth={2}
								d='M6 18L18 6M6 6l12 12'
							/>
						</svg>
					</button>
				</div>

				{/* Form */}
				<div className='p-6 space-y-6'>
					{/* Value Name Input */}
					<div className='space-y-2'>
						<label className='block text-sm font-medium text-gray-700 dark:text-gray-300'>
							Value Name
						</label>
						<div className='relative'>
							<input
								type='text'
								placeholder='e.g., Integrity, Creativity, Family'
								value={valueName}
								onChange={(e) => setValueName(e.target.value)}
								onKeyPress={handleKeyPress}
								className='w-full p-3 pr-10 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder:text-gray-500 focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all'
								disabled={isSubmitting}
							/>
							<div className='absolute right-3 top-1/2 -translate-y-1/2'>
								<svg
									className='w-5 h-5 text-gray-400'
									fill='none'
									stroke='currentColor'
									viewBox='0 0 24 24'>
									<path
										strokeLinecap='round'
										strokeLinejoin='round'
										strokeWidth={2}
										d='M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
									/>
								</svg>
							</div>
						</div>
					</div>

					{/* Quote/Definition Input */}
					<div className='space-y-2'>
						<label className='block text-sm font-medium text-gray-700 dark:text-gray-300'>
							Definition or Quote
						</label>
						<div className='relative'>
							<textarea
								rows={4}
								placeholder='Define your value with a meaningful quote or personal definition...'
								value={quote}
								onChange={(e) => setQuote(e.target.value)}
								onKeyPress={handleKeyPress}
								className='w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder:text-gray-500 focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all resize-none'
								disabled={isSubmitting}
							/>
							<div className='absolute right-3 top-3'>
								<svg
									className='w-5 h-5 text-gray-400'
									fill='none'
									stroke='currentColor'
									viewBox='0 0 24 24'>
									<path
										strokeLinecap='round'
										strokeLinejoin='round'
										strokeWidth={2}
										d='M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z'
									/>
								</svg>
							</div>
						</div>
					</div>
					{/* Public Option */}
					<div className='space-y-2'>
						<label className='block text-sm font-medium text-gray-700 dark:text-gray-300'>
							Visibility
						</label>
						<div className='flex items-center gap-4'>
							<label className='flex items-center gap-2 text-sm text-gray-700 dark:text-gray-300'>
								<input
									type='radio'
									name='visibility'
									checked={!isPublic}
									onChange={() => setIsPublic(false)}
									className='form-radio text-teal-600 focus:ring-teal-500'
								/>
								<span>Private</span>
							</label>
							<label className='flex items-center gap-2 text-sm text-gray-700 dark:text-gray-300'>
								<input
									type='radio'
									name='visibility'
									checked={isPublic}
									onChange={() => setIsPublic(true)}
									className='form-radio text-teal-600 focus:ring-teal-500'
								/>
								<span>Public</span>
							</label>
						</div>
					</div>

					{/* Helper Text */}
					<div className='flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400'>
						<svg
							className='w-4 h-4'
							fill='none'
							stroke='currentColor'
							viewBox='0 0 24 24'>
							<path
								strokeLinecap='round'
								strokeLinejoin='round'
								strokeWidth={2}
								d='M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z'
							/>
						</svg>
						<span>Press Ctrl+Enter to save quickly</span>
					</div>
				</div>

				{/* Actions */}
				<div className='flex items-center justify-between p-6 rounded-b-2xl'>
					<button
						onClick={onClose}
						className='px-6 py-2.5 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white font-medium transition-colors'
						disabled={isSubmitting}>
						Cancel
					</button>
					<button
						onClick={handleSave}
						disabled={isSubmitting || !valueName.trim() || !quote.trim()}
						className='px-6 py-2.5 bg-gradient-to-r from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-700 disabled:from-gray-400 disabled:to-gray-500 text-white rounded-lg font-medium shadow-lg hover:shadow-xl transform transition-all duration-200 hover:scale-[1.02] disabled:hover:scale-100 disabled:cursor-not-allowed flex items-center gap-2'>
						{isSubmitting ? (
							<>
								<div className='w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin'></div>
								<span>Saving...</span>
							</>
						) : (
							<>
								<svg
									className='w-4 h-4'
									fill='none'
									stroke='currentColor'
									viewBox='0 0 24 24'>
									<path
										strokeLinecap='round'
										strokeLinejoin='round'
										strokeWidth={2}
										d='M5 13l4 4L19 7'
									/>
								</svg>
								<span>Save Value</span>
							</>
						)}
					</button>
				</div>
			</div>
		</div>
	);
};

export default AddValueModal;
