import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { siteName } from '@/utils/variables';
import { logo } from '@/assets/images';

const Onboard = () => {
	return (
		<div className='min-h-screen flex flex-col justify-center items-center bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 px-4'>
			{/* Logo Section */}
			<div className='mb-8'>
				<div className='bg-white shadow-xl rounded-2xl p-8 border border-gray-200 dark:border-gray-700'>
					<div className='w-full max-w-sm mx-auto'>
						<Image
							src={logo}
							alt={siteName}
							width={200}
							height={200}
							priority={true}
							className='object-contain w-full h-auto'
						/>
					</div>
				</div>
			</div>

			{/* Hero Text */}
			<div className='text-center mb-12 max-w-md'>
				<h1 className='text-gray-800 dark:text-white text-3xl md:text-4xl font-bold mb-4 leading-tight'>
					Change the Quality of your Thinking
				</h1>
			</div>

			{/* Action Buttons */}
			<div className='flex flex-col gap-4 w-full max-w-sm'>
				<Link
					href='auth/login'
					className='w-full'>
					<button className='w-full bg-white dark:bg-teal-600 hover:bg-gray-50 dark:hover:bg-teal-700 border-2 border-gray-300 dark:border-teal-600 text-gray-800 dark:text-white py-4 px-8 rounded-xl text-lg font-semibold transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 cursor-pointer'>
						Sign In
					</button>
				</Link>

				<Link
					href='auth/signup'
					className='w-full'>
					<button className='w-full bg-gray-900 dark:bg-white hover:bg-gray-800 dark:hover:bg-gray-100 text-white dark:text-gray-900 py-4 px-8 rounded-xl text-lg font-semibold transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 cursor-pointer'>
						Sign Up
					</button>
				</Link>
			</div>

			{/* Footer */}
		</div>
	);
};

export default Onboard;
