'use client';
import React from 'react';
import { createPortal } from 'react-dom';
import { FaTimes } from 'react-icons/fa';
import Image from 'next/image';
import { cn } from '@/src/lib/utils';

const ImageModal = ({
	isOpen,
	onClose,
	src,
	alt = 'Image',
	title,
	closeOnBackdrop = true,
	closeOnEscape = true,
	className = '',
}) => {
	// Handle escape key press
	React.useEffect(() => {
		if (!closeOnEscape || !isOpen) return;

		const handleEscape = (e) => {
			if (e.key === 'Escape') {
				onClose();
			}
		};

		document.addEventListener('keydown', handleEscape);
		return () => document.removeEventListener('keydown', handleEscape);
	}, [closeOnEscape, isOpen, onClose]);

	// Prevent body scroll when modal is open
	React.useEffect(() => {
		if (isOpen) {
			document.body.style.overflow = 'hidden';
		} else {
			document.body.style.overflow = 'unset';
		}

		return () => {
			document.body.style.overflow = 'unset';
		};
	}, [isOpen]);

	const handleBackdropClick = (e) => {
		if (closeOnBackdrop && e.target === e.currentTarget) {
			onClose();
		}
	};

	const handleClose = () => {
		onClose();
	};

	if (!isOpen || !src) return null;

	const modalContent = (
		<div
			className='fixed inset-0 z-50 flex items-center justify-center p-4'
			onClick={handleBackdropClick}>
			{/* Backdrop */}
			<div className='fixed inset-0 bg-black bg-opacity-80 transition-opacity' />

			{/* Modal */}
			<div
				className={cn(
					'relative bg-white dark:bg-gray-900 rounded-lg shadow-2xl transform transition-all max-w-4xl max-h-[90vh] w-full',
					className,
				)}
				role='dialog'
				aria-modal='true'
				aria-labelledby={title ? 'image-modal-title' : undefined}>
				{/* Header */}
				<div className='flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700'>
					{title && (
						<h2
							id='image-modal-title'
							className='text-lg font-semibold text-gray-900 dark:text-white truncate'>
							{title}
						</h2>
					)}
					<button
						type='button'
						className='ml-auto text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800'
						onClick={handleClose}
						aria-label='Close modal'>
						<FaTimes size={20} />
					</button>
				</div>

				{/* Image Content */}
				<div className='relative flex items-center justify-center p-4 max-h-[calc(90vh-120px)]'>
					<div className='relative max-w-full max-h-full'>
						<Image
							src={src}
							alt={alt}
							width={800}
							height={600}
							className='max-w-full max-h-full object-contain rounded-lg'
							style={{
								width: 'auto',
								height: 'auto',
								maxWidth: '100%',
								maxHeight: '100%',
							}}
							priority
						/>
					</div>
				</div>
			</div>
		</div>
	);

	// Render modal in portal
	return createPortal(modalContent, document.body);
};

export default ImageModal;
