/**
 * Values API service
 */
import { api, createQueryString } from './client';

export const valuesService = {
	/**
	 * Get user's values
	 * @param {Object} params - Query parameters
	 * @param {number} params.page - Page number
	 * @param {number} params.limit - Items per page
	 * @returns {Promise} API response
	 */
	getUserValues: async (params = {}) => {
		const queryString = createQueryString(params);
		const url = queryString ? `/api/values?${queryString}` : '/api/values';
		const response = await api.get(url);
		return response.data;
	},

	/**
	 * Get public values
	 * @param {Object} params - Query parameters
	 * @param {number} params.page - Page number
	 * @param {number} params.limit - Items per page
	 * @returns {Promise} API response
	 */
	getPublicValues: async (params = {}) => {
		const queryString = createQueryString(params);
		const url = queryString
			? `/api/values/public?${queryString}`
			: '/api/values/public';
		const response = await api.get(url);
		return response.data;
	},

	/**
	 * Get value by ID
	 * @param {string} id - Value ID
	 * @returns {Promise} API response
	 */
	getValueById: async (id) => {
		const response = await api.get(`/api/values/${id}`);
		return response.data;
	},

	/**
	 * Create new value
	 * @param {Object} valueData - Value data
	 * @returns {Promise} API response
	 */
	createValue: async (valueData) => {
		const response = await api.post('/api/values', valueData);
		return response.data;
	},

	/**
	 * Update value
	 * @param {string} id - Value ID
	 * @param {Object} valueData - Updated value data
	 * @returns {Promise} API response
	 */
	updateValue: async (id, valueData) => {
		const response = await api.put(`/api/values/${id}`, valueData);
		return response.data;
	},

	/**
	 * Delete value
	 * @param {string} id - Value ID
	 * @returns {Promise} API response
	 */
	deleteValue: async (id) => {
		const response = await api.delete(`/api/values/${id}`);
		return response.data;
	},

	/**
	 * Toggle like on a value
	 * @param {string} id - Value ID
	 * @returns {Promise} API response
	 */
	toggleLike: async (id) => {
		const response = await api.put(`/api/values/${id}/like`);
		return response.data;
	},

	/**
	 * Toggle bookmark on a value
	 * @param {string} id - Value ID
	 * @returns {Promise} API response
	 */
	toggleBookmark: async (id) => {
		const response = await api.post(`/api/values/${id}/bookmark`);
		return response.data;
	},

	/**
	 * Add comment to a value
	 * @param {string} id - Value ID
	 * @param {string} text - Comment text
	 * @returns {Promise} API response
	 */
	addComment: async (id, text) => {
		const response = await api.post(`/api/values/${id}/comments`, { text });
		return response.data;
	},
	/**
	 * Delete a comment from a value
	 * @param {string} valueId - ID of the value
	 * @param {string} commentId - ID of the comment to delete
	 * @returns {Promise} API response
	 */
	deleteComment: async (valueId, commentId) => {
		const response = await api.delete(
			`/api/values/${valueId}/comments/${commentId}`,
		);
		return response.data;
	},
};
