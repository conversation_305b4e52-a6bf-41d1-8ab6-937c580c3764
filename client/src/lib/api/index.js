import { authService } from './auth';
import { diaryService } from './diary';
import { goalsService } from './goals';
import { lessonsService } from './lessons';
import { notificationsService } from './notifications';
import { userService } from './user';
import { userProgressService } from './userProgress';
import { valuesService } from './values';

// Export API client and utilities
export { api as apiClient, api, createQueryString } from './client';

// Export all API services
export { authService } from './auth';
export { userService } from './user';
export { lessonsService } from './lessons';
export { diaryService } from './diary';
export { userProgressService } from './userProgress';
export { goalsService } from './goals';
export { valuesService } from './values';
export { notificationsService } from './notifications';

// Legacy compatibility exports (for gradual migration)
export const authAPI = {
	register: (data) => authService.register(data),
	verifyEmail: (data) => authService.verifyEmail(data),
	login: (data) => authService.login(data),
	googleLogin: (data) => authService.googleLogin(data),
	forgotPassword: (data) => authService.forgotPassword(data),
	resetPassword: (data) => authService.resetPassword(data),
	resendOTP: (data) => authService.resendOTP(data),
	refreshToken: (data) => authService.refreshToken(data),
	logout: () => authService.logout(),
};

export const userAPI = {
	getProfile: () => userService.getProfile(),
	updateProfile: (data) => userService.updateProfile(data),
	updateProfileDetails: (data) => userService.updateProfileDetails(data),
	getAllUsers: (params) => userService.getAllUsers(params),
	updateUserRole: (userId, roleData) =>
		userService.updateUserRole(userId, roleData),
	deleteUser: (userId) => userService.deleteUser(userId),
	updateUserStatus: (userId, statusData) =>
		userService.updateUserStatus(userId, statusData),
	uploadAvatar: (formData) => userService.uploadAvatar(formData),
	changePassword: (data) => userService.changePassword(data),
	getUserStats: () => userService.getUserStats(),
	getPreferences: () => userService.getPreferences(),
	updatePreferences: (preferences) =>
		userService.updatePreferences(preferences),
};

export const lessonsAPI = {
	getLessons: (params) => lessonsService.getLessons(params),
	getLessonById: (id) => lessonsService.getLessonById(id),
	createLesson: (data) => lessonsService.createLesson(data),
	updateLesson: (id, data) => lessonsService.updateLesson(id, data),
	deleteLesson: (id) => lessonsService.deleteLesson(id),
	uploadVideo: (formData) => lessonsService.uploadVideo(formData),
	getCategories: () => lessonsService.getCategories(),
	getFeaturedLessons: (limit) => lessonsService.getFeaturedLessons(limit),
	getRecentLessons: (limit) => lessonsService.getRecentLessons(limit),
	searchLessons: (query, filters) =>
		lessonsService.searchLessons(query, filters),
};

export const userProgressAPI = {
	updateProgress: (data) => userProgressService.updateProgress(data),
	getProgress: (lessonId) => userProgressService.getProgress(lessonId),
	getAllProgress: () => userProgressService.getAllProgress(),
	updateReflection: (data) => userProgressService.updateReflection(data),
	deleteReflection: (lessonId, questionIndex) =>
		userProgressService.deleteReflection(lessonId, questionIndex),
	getReflections: (lessonId) => userProgressService.getReflections(lessonId),
	getLearningStats: () => userProgressService.getLearningStats(),
	markLessonCompleted: (lessonId) =>
		userProgressService.markLessonCompleted(lessonId),
	resetProgress: (lessonId) => userProgressService.resetProgress(lessonId),
	getCompletedLessons: (params) =>
		userProgressService.getCompletedLessons(params),
};

export const goalsAPI = {
	getUserGoals: (params) => goalsService.getUserGoals(params),
	getPublicGoals: (params) => goalsService.getPublicGoals(params),
	getGoalById: (id) => goalsService.getGoalById(id),
	createGoal: (data) => goalsService.createGoal(data),
	updateGoal: (id, data) => goalsService.updateGoal(id, data),
	deleteGoal: (id) => goalsService.deleteGoal(id),
	addTask: (goalId, taskData) => goalsService.addTask(goalId, taskData),
	updateTask: (goalId, taskId, taskData) =>
		goalsService.updateTask(goalId, taskId, taskData),
	deleteTask: (goalId, taskId) => goalsService.deleteTask(goalId, taskId),
	completeTask: (goalId, taskId) => goalsService.completeTask(goalId, taskId),
	getGoalStats: () => goalsService.getGoalStats(),
};

export const valuesAPI = {
	getUserValues: (params) => valuesService.getUserValues(params),
	getPublicValues: (params) => valuesService.getPublicValues(params),
	getValueById: (id) => valuesService.getValueById(id),
	createValue: (data) => valuesService.createValue(data),
	updateValue: (id, data) => valuesService.updateValue(id, data),
	deleteValue: (id) => valuesService.deleteValue(id),
	getPredefinedValues: () => valuesService.getPredefinedValues(),
	getValueCategories: () => valuesService.getValueCategories(),
	searchValues: (query, filters) => valuesService.searchValues(query, filters),
};

export const notificationAPI = {
	subscribe: (data) => notificationsService.subscribe(data),
	unsubscribe: (data) => notificationsService.unsubscribe(data),
	sendNotification: (data) => notificationsService.sendNotification(data),
	broadcastNotification: (data) =>
		notificationsService.broadcastNotification(data),
	getNotificationHistory: (params) =>
		notificationsService.getNotificationHistory(params),
	markAsRead: (id) => notificationsService.markAsRead(id),
	getSettings: () => notificationsService.getSettings(),
	updateSettings: (settings) => notificationsService.updateSettings(settings),
};

// Diary API functions (maintaining existing structure)
export const createDiaryEntry = (data) => diaryService.createEntry(data);
export const getDiaryEntries = (params) => diaryService.getEntries(params);
export const getRecentDiaryEntries = (limit) =>
	diaryService.getRecentEntries(limit);
export const getDiaryEntryById = (id) => diaryService.getEntryById(id);
export const getDiaryEntryByDate = (date) => diaryService.getEntryByDate(date);
export const updateDiaryEntry = (id, data) =>
	diaryService.updateEntry(id, data);
export const deleteDiaryEntry = (id) => diaryService.deleteEntry(id);
export const uploadImage = (formData) => diaryService.uploadImage(formData);
