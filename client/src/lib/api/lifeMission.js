import { api, createQueryString } from './client';

export const lifeMissionService = {
	createLifeMission: async (lifeMissionData) => {
		try {
			const response = await api.post('/api/lifemission', lifeMissionData);
			return response.data;
		} catch (error) {
			throw handleError(error, 'Failed to create life mission');
		}
	},

	getMyLifeMissions: async (query = {}) => {
		try {
			const qs = createQueryString(query);
			const response = await api.get(`/api/lifemission/my?${qs}`);
			return response.data;
		} catch (error) {
			throw handleError(error, 'Failed to fetch your life missions');
		}
	},

	getPublicLifeMissions: async (query = {}) => {
		try {
			const qs = createQueryString(query);
			const response = await api.get(`/api/lifemission/public?${qs}`);
			return response.data;
		} catch (error) {
			throw handleError(error, 'Failed to fetch public life missions');
		}
	},

	getLifeMissionById: async (id) => {
		try {
			const response = await api.get(`/api/lifemission/${id}`);
			return response.data;
		} catch (error) {
			throw handleError(error, 'Failed to fetch life mission');
		}
	},

	deleteLifeMission: async (id) => {
		try {
			const response = await api.delete(`/api/lifemission/${id}`);
			return response.data;
		} catch (error) {
			throw handleError(error, 'Failed to delete life mission');
		}
	},

	toggleLike: async (id) => {
		try {
			const response = await api.put(`/api/lifemission/${id}/like`);
			return response.data;
		} catch (error) {
			throw handleError(error, 'Failed to toggle like');
		}
	},

	toggleBookmark: async (id) => {
		try {
			const response = await api.put(`/api/lifemission/${id}/bookmark`);
			return response.data;
		} catch (error) {
			throw handleError(error, 'Failed to toggle bookmark');
		}
	},

	addComment: async (id, text) => {
		try {
			const response = await api.post(`/api/lifemission/${id}/comments`, {
				text,
			});
			return response.data;
		} catch (error) {
			throw handleError(error, 'Failed to add comment');
		}
	},

	deleteComment: async (id, commentId) => {
		try {
			const response = await api.delete(
				`/api/lifemission/${id}/comments/${commentId}`,
			);
			return response.data;
		} catch (error) {
			throw handleError(error, 'Failed to delete comment');
		}
	},

	getBookmarkedLifeMissions: async (query = {}) => {
		try {
			const qs = createQueryString(query);
			const response = await api.get(`/api/lifemission/bookmarked?${qs}`);
			return response.data;
		} catch (error) {
			throw handleError(error, 'Failed to fetch bookmarked life missions');
		}
	},
};

// Reusable error handler
function handleError(error, defaultMsg) {
	return {
		message: error.response?.data?.message || defaultMsg,
		errors: error.response?.data?.errors || [],
		status: error.response?.status,
	};
}
