/**
 * Performance optimization utilities
 */
import { useCallback, useMemo, useRef, useEffect, useState } from 'react';

/**
 * Debounce hook for performance optimization
 * @param {Function} callback - Function to debounce
 * @param {number} delay - Delay in milliseconds
 * @param {Array} deps - Dependencies array
 * @returns {Function} Debounced function
 */
export const useDebounce = (callback, delay, deps = []) => {
	const timeoutRef = useRef(null);

	const debouncedCallback = useCallback(
		(...args) => {
			if (timeoutRef.current) {
				clearTimeout(timeoutRef.current);
			}

			timeoutRef.current = setTimeout(() => {
				callback(...args);
			}, delay);
		},
		[callback, delay, ...deps],
	);

	useEffect(() => {
		return () => {
			if (timeoutRef.current) {
				clearTimeout(timeoutRef.current);
			}
		};
	}, []);

	return debouncedCallback;
};

/**
 * Throttle hook for performance optimization
 * @param {Function} callback - Function to throttle
 * @param {number} limit - Time limit in milliseconds
 * @param {Array} deps - Dependencies array
 * @returns {Function} Throttled function
 */
export const useThrottle = (callback, limit, deps = []) => {
	const inThrottle = useRef(false);

	const throttledCallback = useCallback(
		(...args) => {
			if (!inThrottle.current) {
				callback(...args);
				inThrottle.current = true;
				setTimeout(() => {
					inThrottle.current = false;
				}, limit);
			}
		},
		[callback, limit, ...deps],
	);

	return throttledCallback;
};

/**
 * Memoized value hook with deep comparison
 * @param {Function} factory - Factory function
 * @param {Array} deps - Dependencies array
 * @returns {*} Memoized value
 */
export const useDeepMemo = (factory, deps) => {
	const ref = useRef();

	if (!ref.current || !deepEqual(ref.current.deps, deps)) {
		ref.current = {
			deps,
			value: factory(),
		};
	}

	return ref.current.value;
};

/**
 * Deep equality comparison
 * @param {*} a - First value
 * @param {*} b - Second value
 * @returns {boolean} True if deeply equal
 */
const deepEqual = (a, b) => {
	if (a === b) return true;

	if (a == null || b == null) return false;

	if (Array.isArray(a) && Array.isArray(b)) {
		if (a.length !== b.length) return false;
		for (let i = 0; i < a.length; i++) {
			if (!deepEqual(a[i], b[i])) return false;
		}
		return true;
	}

	if (typeof a === 'object' && typeof b === 'object') {
		const keysA = Object.keys(a);
		const keysB = Object.keys(b);

		if (keysA.length !== keysB.length) return false;

		for (let key of keysA) {
			if (!keysB.includes(key)) return false;
			if (!deepEqual(a[key], b[key])) return false;
		}
		return true;
	}

	return false;
};

/**
 * Intersection Observer hook for lazy loading
 * @param {Object} options - Intersection observer options
 * @returns {Array} [ref, isIntersecting]
 */
export const useIntersectionObserver = (options = {}) => {
	const [isIntersecting, setIsIntersecting] = useState(false);
	const ref = useRef(null);

	useEffect(() => {
		const element = ref.current;
		if (!element) return;

		const observer = new IntersectionObserver(
			([entry]) => {
				setIsIntersecting(entry.isIntersecting);
			},
			{
				threshold: 0.1,
				rootMargin: '50px',
				...options,
			},
		);

		observer.observe(element);

		return () => {
			observer.unobserve(element);
		};
	}, [options]);

	return [ref, isIntersecting];
};

/**
 * Virtual scrolling hook for large lists
 * @param {Array} items - Array of items
 * @param {number} itemHeight - Height of each item
 * @param {number} containerHeight - Height of container
 * @returns {Object} Virtual scrolling data
 */
export const useVirtualScroll = (items, itemHeight, containerHeight) => {
	const [scrollTop, setScrollTop] = useState(0);

	const visibleCount = Math.ceil(containerHeight / itemHeight);
	const startIndex = Math.floor(scrollTop / itemHeight);
	const endIndex = Math.min(startIndex + visibleCount + 1, items.length);

	const visibleItems = useMemo(() => {
		return items.slice(startIndex, endIndex).map((item, index) => ({
			...item,
			index: startIndex + index,
		}));
	}, [items, startIndex, endIndex]);

	const totalHeight = items.length * itemHeight;
	const offsetY = startIndex * itemHeight;

	return {
		visibleItems,
		totalHeight,
		offsetY,
		onScroll: (e) => setScrollTop(e.target.scrollTop),
	};
};

/**
 * Performance monitoring hook
 * @param {string} name - Performance mark name
 * @param {Array} deps - Dependencies to monitor
 */
export const usePerformanceMonitor = (name, deps = []) => {
	useEffect(() => {
		if (typeof window !== 'undefined' && window.performance) {
			performance.mark(`${name}-start`);

			return () => {
				performance.mark(`${name}-end`);
				performance.measure(name, `${name}-start`, `${name}-end`);

				const measure = performance.getEntriesByName(name)[0];
				if (measure && process.env.NODE_ENV === 'development') {
					console.log(
						`Performance: ${name} took ${measure.duration.toFixed(2)}ms`,
					);
				}
			};
		}
	}, deps);
};

/**
 * Memory usage monitoring hook
 */
export const useMemoryMonitor = () => {
	useEffect(() => {
		if (
			typeof window !== 'undefined' &&
			window.performance &&
			window.performance.memory
		) {
			const logMemory = () => {
				const memory = window.performance.memory;
				console.log('Memory Usage:', {
					used: `${(memory.usedJSHeapSize / 1048576).toFixed(2)} MB`,
					total: `${(memory.totalJSHeapSize / 1048576).toFixed(2)} MB`,
					limit: `${(memory.jsHeapSizeLimit / 1048576).toFixed(2)} MB`,
				});
			};

			const interval = setInterval(logMemory, 10000); // Log every 10 seconds

			return () => clearInterval(interval);
		}
	}, []);
};

/**
 * Bundle size analyzer (development only)
 */
export const analyzeBundleSize = () => {
	if (typeof window === 'undefined' && process.env.NODE_ENV === 'development') {
		// Only run on server side
		try {
			const analyzer = require('webpack-bundle-analyzer');
			console.log('Bundle analyzer available at http://localhost:8888');
		} catch (error) {
			console.log('Bundle analyzer not available');
		}
	}
};

/**
 * Image optimization utility
 * @param {string} src - Image source
 * @param {Object} options - Optimization options
 * @returns {string} Optimized image URL
 */
export const optimizeImage = (src, options = {}) => {
	const { width, height, quality = 80, format = 'webp' } = options;

	// If using Cloudinary or similar service
	if (src.includes('cloudinary.com')) {
		let transformations = [];

		if (width) transformations.push(`w_${width}`);
		if (height) transformations.push(`h_${height}`);
		transformations.push(`q_${quality}`);
		transformations.push(`f_${format}`);

		return src.replace('/upload/', `/upload/${transformations.join(',')}/`);
	}

	// For other images, return as-is (could implement other optimizations)
	return src;
};

/**
 * Preload critical resources
 * @param {Array} resources - Array of resource URLs
 */
export const preloadResources = (resources) => {
	if (typeof window === 'undefined') return;

	resources.forEach((resource) => {
		const link = document.createElement('link');
		link.rel = 'preload';
		link.href = resource.url;
		link.as = resource.type || 'fetch';
		if (resource.crossorigin) link.crossOrigin = resource.crossorigin;
		document.head.appendChild(link);
	});
};
