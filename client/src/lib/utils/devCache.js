/**
 * Development cache management utilities
 * These utilities help clear various caches during development
 */

/**
 * Clear all browser caches in development
 */
export const clearAllCaches = async () => {
	if (process.env.NODE_ENV !== 'development') {
		console.warn('Cache clearing is only available in development mode');
		return;
	}

	try {
		// Clear service worker caches
		if ('caches' in window) {
			const cacheNames = await caches.keys();
			await Promise.all(
				cacheNames.map(cacheName => {
					console.log('[Dev] Clearing cache:', cacheName);
					return caches.delete(cacheName);
				})
			);
			console.log('[Dev] All service worker caches cleared');
		}

		// Clear localStorage
		if (typeof window !== 'undefined' && window.localStorage) {
			const itemCount = window.localStorage.length;
			window.localStorage.clear();
			console.log(`[Dev] Cleared ${itemCount} localStorage items`);
		}

		// Clear sessionStorage
		if (typeof window !== 'undefined' && window.sessionStorage) {
			const itemCount = window.sessionStorage.length;
			window.sessionStorage.clear();
			console.log(`[Dev] Cleared ${itemCount} sessionStorage items`);
		}

		// Unregister service worker
		if ('serviceWorker' in navigator) {
			const registrations = await navigator.serviceWorker.getRegistrations();
			await Promise.all(
				registrations.map(registration => {
					console.log('[Dev] Unregistering service worker');
					return registration.unregister();
				})
			);
		}

		console.log('[Dev] All caches cleared successfully');
		
		// Optionally reload the page
		if (window.confirm('All caches cleared! Reload the page to see fresh content?')) {
			window.location.reload();
		}
	} catch (error) {
		console.error('[Dev] Error clearing caches:', error);
	}
};

/**
 * Clear only localStorage items
 */
export const clearLocalStorage = () => {
	if (process.env.NODE_ENV !== 'development') {
		console.warn('Cache clearing is only available in development mode');
		return;
	}

	if (typeof window !== 'undefined' && window.localStorage) {
		const itemCount = window.localStorage.length;
		window.localStorage.clear();
		console.log(`[Dev] Cleared ${itemCount} localStorage items`);
	}
};

/**
 * Clear specific localStorage items
 */
export const clearSpecificLocalStorage = (keys) => {
	if (process.env.NODE_ENV !== 'development') {
		console.warn('Cache clearing is only available in development mode');
		return;
	}

	if (typeof window !== 'undefined' && window.localStorage) {
		keys.forEach(key => {
			window.localStorage.removeItem(key);
			console.log(`[Dev] Cleared localStorage item: ${key}`);
		});
	}
};

/**
 * Force reload service worker
 */
export const reloadServiceWorker = async () => {
	if (process.env.NODE_ENV !== 'development') {
		console.warn('Service worker reload is only available in development mode');
		return;
	}

	try {
		if ('serviceWorker' in navigator) {
			const registrations = await navigator.serviceWorker.getRegistrations();
			
			// Unregister all service workers
			await Promise.all(
				registrations.map(registration => registration.unregister())
			);
			
			// Re-register the service worker
			await navigator.serviceWorker.register('/sw.js');
			console.log('[Dev] Service worker reloaded');
		}
	} catch (error) {
		console.error('[Dev] Error reloading service worker:', error);
	}
};

/**
 * Add development cache controls to window for easy access in console
 */
export const addDevCacheControls = () => {
	if (process.env.NODE_ENV !== 'development' || typeof window === 'undefined') {
		return;
	}

	// Add to window object for console access
	window.devCache = {
		clearAll: clearAllCaches,
		clearLocalStorage,
		clearSpecific: clearSpecificLocalStorage,
		reloadServiceWorker,
		
		// Quick shortcuts
		clear: clearAllCaches,
		reload: () => window.location.reload(),
		
		// Info function
		info: () => {
			console.log(`
🛠️  Development Cache Controls Available:

• window.devCache.clearAll() - Clear all caches and storage
• window.devCache.clearLocalStorage() - Clear only localStorage
• window.devCache.clearSpecific(['key1', 'key2']) - Clear specific localStorage keys
• window.devCache.reloadServiceWorker() - Reload service worker
• window.devCache.reload() - Reload the page

Quick shortcuts:
• window.devCache.clear() - Same as clearAll()
• window.devCache.info() - Show this help

Current localStorage items: ${window.localStorage.length}
Service Worker registered: ${'serviceWorker' in navigator}
			`);
		}
	};

	// Show info on first load
	console.log('🛠️  Development cache controls loaded. Type window.devCache.info() for help.');
};

/**
 * Auto-clear problematic cache items on page load in development
 */
export const autoDevCacheCleanup = () => {
	if (process.env.NODE_ENV !== 'development' || typeof window === 'undefined') {
		return;
	}

	// Clear splash screen cache to prevent stale splash behavior
	const hasSeenSplash = window.localStorage.getItem('hasSeenSplash');
	if (hasSeenSplash) {
		console.log('[Dev] Clearing splash screen cache for fresh development experience');
		window.localStorage.removeItem('hasSeenSplash');
	}

	// Add timestamp to help identify when the page was last loaded
	window.localStorage.setItem('dev_last_load', new Date().toISOString());
};
