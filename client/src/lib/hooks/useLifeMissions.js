import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { setAuthToken } from '../api/client';
import { lifeMissionService } from '../api/lifeMission';
import { isAuthenticated, logout } from './useAuth';

// Get user's life missions
export const useMyLifeMissions = (query = {}) => {
	return useQuery({
		queryKey: ['lifeMissions', 'my', query],
		queryFn: async () => {
			const token = localStorage.getItem('accessToken');
			if (token) {
				setAuthToken(token);
			}
			return await lifeMissionService.getMyLifeMissions(query);
		},
		enabled: isAuthenticated(),
		staleTime: 60 * 1000, // 1 minute
		gcTime: 10 * 60 * 1000, // 10 minutes
		retry: (failureCount, error) => {
			if (error?.response?.status === 401) {
				return false;
			}
			return failureCount < 3;
		},
		meta: {
			onError: (error) => {
				console.error('Error fetching my life missions:', error);
				if (error?.response?.status === 401) {
					logout();
					if (typeof window !== 'undefined') {
						window.dispatchEvent(new Event('auth-change'));
					}
				}
			},
		},
	});
};

// Get public life missions
export const usePublicLifeMissions = (query = {}) => {
	return useQuery({
		queryKey: ['lifeMissions', 'public', query],
		queryFn: async () => {
			const token = localStorage.getItem('accessToken');
			if (token) {
				setAuthToken(token);
			}
			return await lifeMissionService.getPublicLifeMissions(query);
		},
		enabled: isAuthenticated(),
		staleTime: 60 * 1000, // 1 minute
		gcTime: 10 * 60 * 1000, // 10 minutes
		retry: (failureCount, error) => {
			if (error?.response?.status === 401) {
				return false;
			}
			return failureCount < 3;
		},
		meta: {
			onError: (error) => {
				console.error('Error fetching public life missions:', error);
				if (error?.response?.status === 401) {
					logout();
					if (typeof window !== 'undefined') {
						window.dispatchEvent(new Event('auth-change'));
					}
				}
			},
		},
	});
};

// Get bookmarked life missions
export const useBookmarkedLifeMissions = (query = {}) => {
	return useQuery({
		queryKey: ['lifeMissions', 'bookmarked', query],
		queryFn: async () => {
			const token = localStorage.getItem('accessToken');
			if (token) {
				setAuthToken(token);
			}
			return await lifeMissionService.getBookmarkedLifeMissions(query);
		},
		enabled: isAuthenticated(),
		staleTime: 60 * 1000, // 1 minute
		gcTime: 10 * 60 * 1000, // 10 minutes
		retry: (failureCount, error) => {
			if (error?.response?.status === 401) {
				return false;
			}
			return failureCount < 3;
		},
		meta: {
			onError: (error) => {
				console.error('Error fetching bookmarked life missions:', error);
				if (error?.response?.status === 401) {
					logout();
					if (typeof window !== 'undefined') {
						window.dispatchEvent(new Event('auth-change'));
					}
				}
			},
		},
	});
};

// Get specific life mission by ID
export const useLifeMissionById = (id) => {
	return useQuery({
		queryKey: ['lifeMissions', id],
		queryFn: async () => {
			const token = localStorage.getItem('accessToken');
			if (token) {
				setAuthToken(token);
			}
			return await lifeMissionService.getLifeMissionById(id);
		},
		enabled: isAuthenticated() && !!id,
		staleTime: 30 * 1000, // 30 seconds
		gcTime: 5 * 60 * 1000, // 5 minutes
		retry: (failureCount, error) => {
			if (error?.response?.status === 401) {
				return false;
			}
			return failureCount < 3;
		},
		meta: {
			onError: (error) => {
				console.error('Error fetching life mission:', error);
				if (error?.response?.status === 401) {
					logout();
					if (typeof window !== 'undefined') {
						window.dispatchEvent(new Event('auth-change'));
					}
				}
			},
		},
	});
};

// Create life mission mutation
export const useCreateLifeMission = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (missionData) => {
			const token = localStorage.getItem('accessToken');
			if (token) {
				setAuthToken(token);
			}
			return await lifeMissionService.createLifeMission(missionData);
		},
		onSuccess: (data, variables) => {
			queryClient.invalidateQueries({ queryKey: ['lifeMissions'] });
			console.log('Life mission created successfully:', data);
		},
		onError: (error) => {
			console.error('Error creating life mission:', error);
			if (error?.response?.status === 401) {
				logout();
				if (typeof window !== 'undefined') {
					window.dispatchEvent(new Event('auth-change'));
				}
			}
		},
	});
};

// Delete life mission mutation
export const useDeleteLifeMission = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (id) => {
			const token = localStorage.getItem('accessToken');
			if (token) {
				setAuthToken(token);
			}
			return await lifeMissionService.deleteLifeMission(id);
		},
		onSuccess: (data, variables) => {
			queryClient.invalidateQueries({ queryKey: ['lifeMissions'] });
			console.log('Life mission deleted successfully:', data);
		},
		onError: (error) => {
			console.error('Error deleting life mission:', error);
			if (error?.response?.status === 401) {
				logout();
				if (typeof window !== 'undefined') {
					window.dispatchEvent(new Event('auth-change'));
				}
			}
		},
	});
};

// Toggle like mutation
export const useToggleLike = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (id) => {
			const token = localStorage.getItem('accessToken');
			if (token) {
				setAuthToken(token);
			}
			return await lifeMissionService.toggleLike(id);
		},
		onSuccess: (data, variables) => {
			// Invalidate specific life mission and all life missions queries
			queryClient.invalidateQueries({ queryKey: ['lifeMissions', variables] });
			queryClient.invalidateQueries({ queryKey: ['lifeMissions'] });
			console.log('Like toggled successfully:', data);
		},
		onError: (error) => {
			console.error('Error toggling like:', error);
			if (error?.response?.status === 401) {
				logout();
				if (typeof window !== 'undefined') {
					window.dispatchEvent(new Event('auth-change'));
				}
			}
		},
	});
};

// Toggle bookmark mutation
export const useToggleBookmark = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (id) => {
			const token = localStorage.getItem('accessToken');
			if (token) {
				setAuthToken(token);
			}
			return await lifeMissionService.toggleBookmark(id);
		},
		onSuccess: (data, variables) => {
			// Invalidate specific life mission and all life missions queries
			queryClient.invalidateQueries({ queryKey: ['lifeMissions', variables] });
			queryClient.invalidateQueries({ queryKey: ['lifeMissions'] });
			console.log('Bookmark toggled successfully:', data);
		},
		onError: (error) => {
			console.error('Error toggling bookmark:', error);
			if (error?.response?.status === 401) {
				logout();
				if (typeof window !== 'undefined') {
					window.dispatchEvent(new Event('auth-change'));
				}
			}
		},
	});
};

// Add comment mutation
export const useAddComment = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async ({ id, text }) => {
			const token = localStorage.getItem('accessToken');
			if (token) {
				setAuthToken(token);
			}
			return await lifeMissionService.addComment(id, text);
		},
		onSuccess: (data, variables) => {
			// Invalidate specific life mission to show new comment
			queryClient.invalidateQueries({
				queryKey: ['lifeMissions', variables.id],
			});
			console.log('Comment added successfully:', data);
		},
		onError: (error) => {
			console.error('Error adding comment:', error);
			if (error?.response?.status === 401) {
				logout();
				if (typeof window !== 'undefined') {
					window.dispatchEvent(new Event('auth-change'));
				}
			}
		},
	});
};

// Delete comment mutation
export const useDeleteComment = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async ({ id, commentId }) => {
			const token = localStorage.getItem('accessToken');
			if (token) {
				setAuthToken(token);
			}
			return await lifeMissionService.deleteComment(id, commentId);
		},
		onSuccess: (data, variables) => {
			// Invalidate specific life mission to remove deleted comment
			queryClient.invalidateQueries({
				queryKey: ['lifeMissions', variables.id],
			});
			console.log('Comment deleted successfully:', data);
		},
		onError: (error) => {
			console.error('Error deleting comment:', error);
			if (error?.response?.status === 401) {
				logout();
				if (typeof window !== 'undefined') {
					window.dispatchEvent(new Event('auth-change'));
				}
			}
		},
	});
};
