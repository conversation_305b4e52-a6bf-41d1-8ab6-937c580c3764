import { useEffect, useState } from 'react';
import axios from 'axios';
import { api } from '../api';

export const usePaginatedProfiles = () => {
	const [profiles, setProfiles] = useState([]);
	const [page, setPage] = useState(1);
	const [hasMore, setHasMore] = useState(true);
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState(null);

	const fetchProfiles = async (pageNum = 1) => {
		setLoading(true);
		try {
			const res = await api.get(`/api/user/allUser?${pageNum}&limit=10`);
			const newProfiles = res.data?.data || [];

			setProfiles((prev) => [...prev, ...newProfiles]);
			setHasMore(newProfiles.length > 0);
			setPage(pageNum);
		} catch (err) {
			setError(err);
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		fetchProfiles(1);
	}, []);

	const loadMore = () => {
		if (!loading && hasMore) {
			fetchProfiles(page + 1);
		}
	};

	return { profiles, loading, error, loadMore, hasMore };
};
