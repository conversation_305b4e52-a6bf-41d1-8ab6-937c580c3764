import { useState, useEffect, useCallback, useMemo } from 'react';
import { useIntersectionObserver } from '../utils/performance';
import { usePublicGoals } from './useGoal';
import { usePublicValues } from './useValues';
import { usePublicLifeMissions } from './useLifeMissions';
import { useUserProfile } from './useUser';

// Constants
const POSTS_PER_PAGE = 10;

// Helper function to check if current user has liked/bookmarked an item
const checkUserInteraction = (interactionArray, currentUserId) => {
	if (!interactionArray || !currentUserId) return false;
	return interactionArray.some(
		(item) =>
			(typeof item === 'string' ? item : item._id || item.userId) ===
			currentUserId,
	);
};

// Helper function to extract user ID from profile
const extractUserId = (userProfile) => {
	return (
		userProfile?.data?.user?._id ||
		userProfile?.data?._id ||
		userProfile?.user?._id ||
		userProfile?._id
	);
};

// Helper function to extract user profile
const extractUserProfile = (userProfile) => {
	return (
		userProfile?.data?.user ||
		userProfile?.data ||
		userProfile?.user ||
		userProfile
	);
};

/**
 * Transform different post types into unified format
 */
const transformPostData = (data, type, currentUserId) => {
	if (!data) return [];

	const posts = [];
	const typeConfig = {
		goal: {
			key: 'goals',
			contentField: 'title',
			descriptionField: 'description',
			label: 'a goal',
		},
		value: {
			key: 'values',
			contentField: 'name',
			descriptionField: 'definition',
			label: 'a core value',
		},
		lifeMission: {
			key: 'data',
			contentField: 'mission',
			descriptionField: null,
			label: 'a life mission',
		},
	};

	const config = typeConfig[type];
	if (!config) return [];

	const items =
		type === 'lifeMission' ? data[config.key] : data.data?.[config.key];
	if (!Array.isArray(items)) return [];

	return items.map((item) => ({
		id: `${type}-${item._id}`,
		originalId: item._id,
		type,
		author: {
			name: item.userId?.name || 'Anonymous',
			id: item.userId?._id,
			avatar: item.userId?.avatar,
		},
		content: item[config.contentField] || '',
		description: config.descriptionField
			? item[config.descriptionField] || ''
			: '',
		likes: item.likes?.length || 0,
		comments: item.comments?.length || 0,
		commentData: item.comments || [],
		isLiked: checkUserInteraction(item.likes, currentUserId),
		isBookmarked: checkUserInteraction(item.bookmarks, currentUserId),
		createdAt: item.createdAt,
		postTypeLabel: config.label,
	}));
};

/**
 * Check if there's more data to load from pagination info
 */
const checkHasMore = (goalsData, valuesData, lifeMissionsData) => {
	const goalsPagination = goalsData?.data?.pagination;
	const valuesPagination = valuesData?.data?.pagination;
	const missionsPagination = lifeMissionsData?.pagination;

	const hasMoreGoals =
		goalsPagination?.hasNext ||
		goalsPagination?.page < goalsPagination?.totalPages;
	const hasMoreValues =
		valuesPagination?.hasNext ||
		valuesPagination?.page < valuesPagination?.totalPages;
	const hasMoreMissions = missionsPagination?.page < missionsPagination?.pages;

	return hasMoreGoals || hasMoreValues || hasMoreMissions;
};

/**
 * Custom hook for infinite scrolling community posts
 * Combines data from goals, values, and life missions with infinite scroll functionality
 */
export const useInfiniteCommunityPosts = () => {
	const [page, setPage] = useState(1);
	const [allPosts, setAllPosts] = useState([]);
	const [hasMore, setHasMore] = useState(true);
	const [isLoadingMore, setIsLoadingMore] = useState(false);

	// Get current user profile
	const {
		data: userProfile,
		isLoading: isUserLoading,
		error: userError,
	} = useUserProfile();

	// Extract current user ID and profile
	const currentUserId = useMemo(
		() => extractUserId(userProfile),
		[userProfile],
	);
	const currentUserProfile = useMemo(
		() => extractUserProfile(userProfile),
		[userProfile],
	);

	// Fetch data from all three sources
	const fetchParams = useMemo(
		() => ({
			page,
			limit: POSTS_PER_PAGE,
			sortBy: 'createdAt',
			sortOrder: 'desc',
		}),
		[page],
	);

	const {
		data: goalsData,
		isLoading: goalsLoading,
		error: goalsError,
	} = usePublicGoals(fetchParams);

	const {
		data: valuesData,
		isLoading: valuesLoading,
		error: valuesError,
	} = usePublicValues(fetchParams);

	const {
		data: lifeMissionsData,
		isLoading: lifeMissionsLoading,
		error: lifeMissionsError,
	} = usePublicLifeMissions(fetchParams);

	// Transform and combine all posts
	const transformedPosts = useMemo(() => {
		const goalPosts = transformPostData(goalsData, 'goal', currentUserId);
		const valuePosts = transformPostData(valuesData, 'value', currentUserId);
		const missionPosts = transformPostData(
			lifeMissionsData,
			'lifeMission',
			currentUserId,
		);

		const allTransformed = [...goalPosts, ...valuePosts, ...missionPosts];

		// Sort by creation date (newest first)
		return allTransformed.sort(
			(a, b) => new Date(b.createdAt) - new Date(a.createdAt),
		);
	}, [goalsData, valuesData, lifeMissionsData, currentUserId]);

	// Update posts when new data arrives
	useEffect(() => {
		if (transformedPosts.length > 0) {
			setAllPosts((prevPosts) => {
				if (page === 1) {
					return transformedPosts;
				}

				// Avoid duplicates when loading more
				const existingIds = new Set(prevPosts.map((post) => post.id));
				const newPosts = transformedPosts.filter(
					(post) => !existingIds.has(post.id),
				);
				return [...prevPosts, ...newPosts];
			});

			// Update hasMore based on pagination info
			setHasMore(checkHasMore(goalsData, valuesData, lifeMissionsData));
			setIsLoadingMore(false);
		}
	}, [transformedPosts, page, goalsData, valuesData, lifeMissionsData]);

	// Loading and error states
	const isInitialLoading = useMemo(
		() => page === 1 && (goalsLoading || valuesLoading || lifeMissionsLoading),
		[page, goalsLoading, valuesLoading, lifeMissionsLoading],
	);

	const error = goalsError || valuesError || lifeMissionsError;

	// Load more function
	const loadMore = useCallback(() => {
		if (!isLoadingMore && hasMore && !isInitialLoading) {
			setIsLoadingMore(true);
			setPage((prev) => prev + 1);
		}
	}, [isLoadingMore, hasMore, isInitialLoading]);

	// Intersection observer for infinite scroll
	const [sentinelRef, isIntersecting] = useIntersectionObserver({
		threshold: 0.1,
		rootMargin: '100px',
	});

	// Trigger load more when sentinel is visible
	useEffect(() => {
		if (isIntersecting && hasMore && !isLoadingMore && !isInitialLoading) {
			loadMore();
		}
	}, [isIntersecting, hasMore, isLoadingMore, isInitialLoading, loadMore]);

	// Reset function for refreshing
	const reset = useCallback(() => {
		setPage(1);
		setAllPosts([]);
		setHasMore(true);
		setIsLoadingMore(false);
	}, []);

	// Optimistic update functions
	const updatePostLike = useCallback((postId, isLiked) => {
		setAllPosts((prevPosts) =>
			prevPosts.map((post) =>
				post.id === postId
					? {
							...post,
							isLiked,
							likes: isLiked ? post.likes + 1 : Math.max(0, post.likes - 1),
					  }
					: post,
			),
		);
	}, []);

	const updatePostBookmark = useCallback((postId, isBookmarked) => {
		setAllPosts((prevPosts) =>
			prevPosts.map((post) =>
				post.id === postId ? { ...post, isBookmarked } : post,
			),
		);
	}, []);

	const updatePostComments = useCallback((postId, increment = true) => {
		setAllPosts((prevPosts) =>
			prevPosts.map((post) =>
				post.id === postId
					? {
							...post,
							comments: increment
								? post.comments + 1
								: Math.max(0, post.comments - 1),
					  }
					: post,
			),
		);
	}, []);

	return {
		posts: allPosts,
		isLoading: isInitialLoading,
		isLoadingMore,
		error,
		hasMore,
		loadMore,
		reset,
		sentinelRef,
		// Additional metadata
		totalPosts: allPosts.length,
		currentPage: page,
		currentUserId,
		userProfile: currentUserProfile,
		// Optimistic update functions
		updatePostLike,
		updatePostBookmark,
		updatePostComments,
	};
};
