import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { setAuthToken } from '../api/client';
import { isAuthenticated, logout } from './useAuth';
import { goalsService } from '../api';

// Get user's goals
export const useUserGoals = (query = {}) => {
	return useQuery({
		queryKey: ['goals', 'user', query],
		queryFn: async () => {
			const token = localStorage.getItem('accessToken');
			if (token) {
				setAuthToken(token);
			}
			return await goalsService.getUserGoals(query);
		},
		enabled: isAuthenticated(),
		staleTime: 60 * 1000, // 1 minute
		gcTime: 10 * 60 * 1000, // 10 minutes
		retry: (failureCount, error) => {
			if (error?.response?.status === 401) {
				return false;
			}
			return failureCount < 3;
		},
		meta: {
			onError: (error) => {
				console.error('Error fetching user goals:', error);
				if (error?.response?.status === 401) {
					logout();
					if (typeof window !== 'undefined') {
						window.dispatchEvent(new Event('auth-change'));
					}
				}
			},
		},
	});
};

// Get public goals
export const usePublicGoals = (query = {}) => {
	return useQuery({
		queryKey: ['goals', 'public', query],
		queryFn: async () => {
			const token = localStorage.getItem('accessToken');
			if (token) {
				setAuthToken(token);
			}
			return await goalsService.getPublicGoals(query);
		},
		enabled: isAuthenticated(),
		staleTime: 60 * 1000, // 1 minute
		gcTime: 10 * 60 * 1000, // 10 minutes
		retry: (failureCount, error) => {
			if (error?.response?.status === 401) {
				return false;
			}
			return failureCount < 3;
		},
		meta: {
			onError: (error) => {
				console.error('Error fetching public goals:', error);
				if (error?.response?.status === 401) {
					logout();
					if (typeof window !== 'undefined') {
						window.dispatchEvent(new Event('auth-change'));
					}
				}
			},
		},
	});
};

// Get specific goal by ID
export const useGoalById = (id) => {
	return useQuery({
		queryKey: ['goals', id],
		queryFn: async () => {
			const token = localStorage.getItem('accessToken');
			if (token) {
				setAuthToken(token);
			}
			return await goalsService.getGoalById(id);
		},
		enabled: isAuthenticated() && !!id,
		staleTime: 30 * 1000, // 30 seconds
		gcTime: 5 * 60 * 1000, // 5 minutes
		retry: (failureCount, error) => {
			if (error?.response?.status === 401) {
				return false;
			}
			return failureCount < 3;
		},
		meta: {
			onError: (error) => {
				console.error('Error fetching goal:', error);
				if (error?.response?.status === 401) {
					logout();
					if (typeof window !== 'undefined') {
						window.dispatchEvent(new Event('auth-change'));
					}
				}
			},
		},
	});
};

// Get goal stats
export const useGoalStats = () => {
	return useQuery({
		queryKey: ['goals', 'stats'],
		queryFn: async () => {
			const token = localStorage.getItem('accessToken');
			if (token) {
				setAuthToken(token);
			}
			return await goalsService.getGoalStats();
		},
		enabled: isAuthenticated(),
		staleTime: 2 * 60 * 1000, // 2 minutes
		gcTime: 10 * 60 * 1000, // 10 minutes
		retry: (failureCount, error) => {
			if (error?.response?.status === 401) {
				return false;
			}
			return failureCount < 3;
		},
		meta: {
			onError: (error) => {
				console.error('Error fetching goal stats:', error);
				if (error?.response?.status === 401) {
					logout();
					if (typeof window !== 'undefined') {
						window.dispatchEvent(new Event('auth-change'));
					}
				}
			},
		},
	});
};

// Create goal mutation
export const useCreateGoal = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (goalData) => {
			const token = localStorage.getItem('accessToken');
			if (token) {
				setAuthToken(token);
			}
			return await goalsService.createGoal(goalData);
		},
		onSuccess: (data, variables) => {
			queryClient.invalidateQueries({ queryKey: ['goals'] });
			console.log('Goal created successfully:', data);
		},
		onError: (error) => {
			console.error('Error creating goal:', error);
			if (error?.response?.status === 401) {
				logout();
				if (typeof window !== 'undefined') {
					window.dispatchEvent(new Event('auth-change'));
				}
			}
		},
	});
};

// Update goal mutation
export const useUpdateGoal = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async ({ id, goalData }) => {
			const token = localStorage.getItem('accessToken');
			if (token) {
				setAuthToken(token);
			}
			return await goalsService.updateGoal(id, goalData);
		},
		onSuccess: (data, variables) => {
			queryClient.invalidateQueries({ queryKey: ['goals', variables.id] });
			queryClient.invalidateQueries({ queryKey: ['goals'] });
			console.log('Goal updated successfully:', data);
		},
		onError: (error) => {
			console.error('Error updating goal:', error);
			if (error?.response?.status === 401) {
				logout();
				if (typeof window !== 'undefined') {
					window.dispatchEvent(new Event('auth-change'));
				}
			}
		},
	});
};

// Delete goal mutation
export const useDeleteGoal = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (id) => {
			const token = localStorage.getItem('accessToken');
			if (token) {
				setAuthToken(token);
			}
			return await goalsService.deleteGoal(id);
		},
		onSuccess: (data, variables) => {
			queryClient.invalidateQueries({ queryKey: ['goals'] });
			console.log('Goal deleted successfully:', data);
		},
		onError: (error) => {
			console.error('Error deleting goal:', error);
			if (error?.response?.status === 401) {
				logout();
				if (typeof window !== 'undefined') {
					window.dispatchEvent(new Event('auth-change'));
				}
			}
		},
	});
};

// Add task mutation
export const useAddTask = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async ({ goalId, taskData }) => {
			const token = localStorage.getItem('accessToken');
			if (token) {
				setAuthToken(token);
			}
			return await goalsService.addTask(goalId, taskData);
		},
		onSuccess: (data, variables) => {
			queryClient.invalidateQueries({ queryKey: ['goals', variables.goalId] });
			queryClient.invalidateQueries({ queryKey: ['goals'] });
			console.log('Task added successfully:', data);
		},
		onError: (error) => {
			console.error('Error adding task:', error);
			if (error?.response?.status === 401) {
				logout();
				if (typeof window !== 'undefined') {
					window.dispatchEvent(new Event('auth-change'));
				}
			}
		},
	});
};

// Update task mutation
export const useUpdateTask = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async ({ goalId, taskId, taskData }) => {
			const token = localStorage.getItem('accessToken');
			if (token) {
				setAuthToken(token);
			}
			return await goalsService.updateTask(goalId, taskId, taskData);
		},
		onSuccess: (data, variables) => {
			queryClient.invalidateQueries({ queryKey: ['goals', variables.goalId] });
			queryClient.invalidateQueries({ queryKey: ['goals'] });
			console.log('Task updated successfully:', data);
		},
		onError: (error) => {
			console.error('Error updating task:', error);
			if (error?.response?.status === 401) {
				logout();
				if (typeof window !== 'undefined') {
					window.dispatchEvent(new Event('auth-change'));
				}
			}
		},
	});
};

// Delete task mutation
export const useDeleteTask = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async ({ goalId, taskId }) => {
			const token = localStorage.getItem('accessToken');
			if (token) {
				setAuthToken(token);
			}
			return await goalsService.deleteTask(goalId, taskId);
		},
		onSuccess: (data, variables) => {
			queryClient.invalidateQueries({ queryKey: ['goals', variables.goalId] });
			queryClient.invalidateQueries({ queryKey: ['goals'] });
			console.log('Task deleted successfully:', data);
		},
		onError: (error) => {
			console.error('Error deleting task:', error);
			if (error?.response?.status === 401) {
				logout();
				if (typeof window !== 'undefined') {
					window.dispatchEvent(new Event('auth-change'));
				}
			}
		},
	});
};

// Complete task mutation
export const useCompleteTask = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async ({ goalId, taskId }) => {
			const token = localStorage.getItem('accessToken');
			if (token) {
				setAuthToken(token);
			}
			return await goalsService.completeTask(goalId, taskId);
		},
		onSuccess: (data, variables) => {
			queryClient.invalidateQueries({ queryKey: ['goals', variables.goalId] });
			queryClient.invalidateQueries({ queryKey: ['goals'] });
			console.log('Task completed successfully:', data);
		},
		onError: (error) => {
			console.error('Error completing task:', error);
			if (error?.response?.status === 401) {
				logout();
				if (typeof window !== 'undefined') {
					window.dispatchEvent(new Event('auth-change'));
				}
			}
		},
	});
};

// Toggle like mutation
export const useToggleLike = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (id) => {
			const token = localStorage.getItem('accessToken');
			if (token) {
				setAuthToken(token);
			}
			return await goalsService.toggleLike(id);
		},
		onSuccess: (data, variables) => {
			// Invalidate specific goal and all goals queries
			queryClient.invalidateQueries({ queryKey: ['goals', variables] });
			queryClient.invalidateQueries({ queryKey: ['goals'] });
			console.log('Like toggled successfully:', data);
		},
		onError: (error) => {
			console.error('Error toggling like:', error);
			if (error?.response?.status === 401) {
				logout();
				if (typeof window !== 'undefined') {
					window.dispatchEvent(new Event('auth-change'));
				}
			}
		},
	});
};

// Toggle bookmark mutation
export const useToggleBookmark = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (id) => {
			const token = localStorage.getItem('accessToken');
			if (token) {
				setAuthToken(token);
			}
			return await goalsService.toggleBookmark(id);
		},
		onSuccess: (data, variables) => {
			// Invalidate specific goal and all goals queries
			queryClient.invalidateQueries({ queryKey: ['goals', variables] });
			queryClient.invalidateQueries({ queryKey: ['goals'] });
			console.log('Bookmark toggled successfully:', data);
		},
		onError: (error) => {
			console.error('Error toggling bookmark:', error);
			if (error?.response?.status === 401) {
				logout();
				if (typeof window !== 'undefined') {
					window.dispatchEvent(new Event('auth-change'));
				}
			}
		},
	});
};

// Add comment mutation
export const useAddComment = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async ({ id, text }) => {
			const token = localStorage.getItem('accessToken');
			if (token) {
				setAuthToken(token);
			}
			return await goalsService.addComment(id, text);
		},
		onSuccess: (data, variables) => {
			// Invalidate specific goal to show new comment
			queryClient.invalidateQueries({
				queryKey: ['goals', variables.id],
			});
			console.log('Comment added successfully:', data);
		},
		onError: (error) => {
			console.error('Error adding comment:', error);
			if (error?.response?.status === 401) {
				logout();
				if (typeof window !== 'undefined') {
					window.dispatchEvent(new Event('auth-change'));
				}
			}
		},
	});
};

// Delete comment mutation
export const useDeleteComment = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async ({ id, commentId }) => {
			const token = localStorage.getItem('accessToken');
			if (token) {
				setAuthToken(token);
			}
			return await goalsService.deleteComment(id, commentId);
		},
		onSuccess: (data, variables) => {
			// Invalidate specific goal to remove deleted comment
			queryClient.invalidateQueries({
				queryKey: ['goals', variables.id],
			});
			console.log('Comment deleted successfully:', data);
		},
		onError: (error) => {
			console.error('Error deleting comment:', error);
			if (error?.response?.status === 401) {
				logout();
				if (typeof window !== 'undefined') {
					window.dispatchEvent(new Event('auth-change'));
				}
			}
		},
	});
};
