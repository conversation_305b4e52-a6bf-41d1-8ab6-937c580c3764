'use client';
import { SplashScreen } from '@/src/components/common';
import Onboard from '@/src/components/common/Onboard';
import HomePage from '@/src/components/Home/HomePage';
import AuthErrorBoundary from '@/src/components/layout/AuthErrorBoundary';
import { isAuthenticated, setHeaderAuthorization } from '@/src/lib/hooks';
import { useEffect, useState } from 'react';

export default function Home() {
	const [showSplash, setShowSplash] = useState(() => {
		if (typeof window !== 'undefined') {
			return localStorage.getItem('hasSeenSplash') !== 'true';
		}
		return true;
	});

	const [isUserAuthenticated, setIsUserAuthenticated] = useState(false);
	const [isCheckingAuth, setIsCheckingAuth] = useState(true);

	useEffect(() => {
		if (typeof window === 'undefined') return;

		const hasSeenSplash = localStorage.getItem('hasSeenSplash');

		if (!hasSeenSplash) {
			const timer = setTimeout(() => {
				setShowSplash(false);
				localStorage.setItem('hasSeenSplash', 'true');
			}, 3000);

			return () => clearTimeout(timer);
		} else {
			setShowSplash(false);
		}
	}, []);

	useEffect(() => {
		// Development cache management
		if (process.env.NODE_ENV === 'development') {
			// Add development cache controls to window
			window.devCache = {
				clearAll: async () => {
					try {
						// Clear service worker caches
						if ('caches' in window) {
							const cacheNames = await caches.keys();
							await Promise.all(
								cacheNames.map((cacheName) => {
									console.log('[Dev] Clearing cache:', cacheName);
									return caches.delete(cacheName);
								}),
							);
						}

						// Clear localStorage (except auth token if you want to stay logged in)
						const token = localStorage.getItem('accessToken');
						localStorage.clear();
						if (
							token &&
							window.confirm('Keep authentication? (Click Cancel to log out)')
						) {
							localStorage.setItem('accessToken', token);
						}

						// Clear sessionStorage
						sessionStorage.clear();

						console.log('[Dev] All caches cleared! Refreshing page...');
						window.location.reload();
					} catch (error) {
						console.error('[Dev] Error clearing caches:', error);
					}
				},

				clearStorage: () => {
					const token = localStorage.getItem('accessToken');
					localStorage.clear();
					sessionStorage.clear();
					if (token && window.confirm('Keep authentication?')) {
						localStorage.setItem('accessToken', token);
					}
					console.log('[Dev] Storage cleared!');
				},

				info: () => {
					console.log(`
🛠️  Development Cache Controls:
• window.devCache.clearAll() - Clear all caches and reload
• window.devCache.clearStorage() - Clear only storage
• window.devCache.info() - Show this help

Press Ctrl+Shift+R (or Cmd+Shift+R) for hard refresh
					`);
				},
			};

			console.log(
				'🛠️  Dev mode: Type window.devCache.info() for cache controls',
			);
		}

		const checkAuthStatus = () => {
			setIsCheckingAuth(true);
			const authenticated = isAuthenticated();
			setIsUserAuthenticated(authenticated);

			if (authenticated) {
				const token = localStorage.getItem('accessToken');
				setHeaderAuthorization(token);
			} else {
				setHeaderAuthorization(null);
			}
			setIsCheckingAuth(false);
		};

		checkAuthStatus();

		const handleStorageChange = (e) => {
			if (e.key === 'accessToken' || e.key === 'refreshToken') {
				checkAuthStatus();
			}
		};

		const handleAuthChange = () => {
			checkAuthStatus();
		};

		// Listen for notification click messages from service worker
		const handleServiceWorkerMessage = (event) => {
			if (event.data?.type === 'NOTIFICATION_CLICK') {
				const { url } = event.data;
				if (url && typeof window !== 'undefined') {
					// Navigate to the URL
					window.location.href = url;
				}
			}
		};

		window.addEventListener('storage', handleStorageChange);
		window.addEventListener('auth-change', handleAuthChange);

		// Listen for service worker messages
		if ('serviceWorker' in navigator) {
			navigator.serviceWorker.addEventListener(
				'message',
				handleServiceWorkerMessage,
			);
		}

		return () => {
			window.removeEventListener('storage', handleStorageChange);
			window.removeEventListener('auth-change', handleAuthChange);
			if ('serviceWorker' in navigator) {
				navigator.serviceWorker.removeEventListener(
					'message',
					handleServiceWorkerMessage,
				);
			}
		};
	}, []);

	if (showSplash || isCheckingAuth) {
		return (
			<div className='w-screen h-screen'>
				<SplashScreen />
			</div>
		);
	}

	return (
		<div className='w-screen h-screen'>
			{isUserAuthenticated ? (
				<AuthErrorBoundary>
					<HomePage />
				</AuthErrorBoundary>
			) : (
				<Onboard />
			)}
		</div>
	);
}
