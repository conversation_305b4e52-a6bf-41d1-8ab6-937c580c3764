'use client';
import React, { useState, useEffect } from 'react';
import { User, Mail, Phone, ArrowLeft, ArrowRight, Check } from 'lucide-react';
import { Calendar } from '@/src/components/ui/calendar';
import toast, { Toaster } from 'react-hot-toast';
import {
	useAvailableSlots,
	useCreateBooking,
} from '@/src/lib/hooks/useBookings';
import { TimeSlotSelector } from '@/src/components/features/booking';
import FixedHeader from '@/src/components/layout/FixedHeader';
import Sidebar from '@/src/components/layout/Sidebar';

// Progress Indicator Component
const ProgressIndicator = ({ currentStep, totalSteps = 3 }) => {
	return (
		<div className='flex items-center justify-center mb-8'>
			{Array.from({ length: totalSteps }, (_, index) => {
				const stepNumber = index + 1;
				return (
					<React.Fragment key={stepNumber}>
						<div
							className={`w-10 h-10 rounded-full flex items-center justify-center border-2 transition-all duration-300 ${
								currentStep >= stepNumber
									? 'bg-teal-500 border-teal-500 text-white'
									: 'border-gray-600 text-gray-400'
							}`}>
							{currentStep > stepNumber ? <Check size={20} /> : stepNumber}
						</div>
						{stepNumber < totalSteps && (
							<div
								className={`w-16 h-0.5 transition-all duration-300 ${
									currentStep > stepNumber ? 'bg-teal-500' : 'bg-gray-600'
								}`}
							/>
						)}
					</React.Fragment>
				);
			})}
		</div>
	);
};

// Coach Info Step Component
const CoachInfoStep = ({ coach, onNext }) => {
	return (
		<div className='text-center space-y-6'>
			<div className='dark:bg-gray-800/50 bg-white/50 backdrop-blur rounded-2xl p-8 border border-gray-700'>
				<div className='w-32 h-32 bg-gradient-to-br from-teal-500 to-teal-600 rounded-full mx-auto mb-6 flex items-center justify-center'>
					<User
						size={48}
						className='text-white'
					/>
				</div>
				<h2 className='text-3xl font-bold mb-2 bg-teal-400 bg-clip-text text-transparent'>
					Book a Session with {coach.name}
				</h2>
				<div className='space-y-4 text-left max-w-md mx-auto'>
					<div className='flex items-center space-x-3'>
						<div className='w-2 h-2 bg-teal-500 rounded-full'></div>
						<span>
							<strong>Coach:</strong> {coach.name}
						</span>
					</div>
					<div className='flex items-center space-x-3'>
						<div className='w-2 h-2 bg-teal-500 rounded-full'></div>
						<span>
							<strong>Title:</strong> {coach.title}
						</span>
					</div>
					<div className='flex items-start space-x-3'>
						<div className='w-2 h-2 bg-teal-500 rounded-full mt-2'></div>
						<div>
							<strong>About:</strong>
							<p className='text-gray-700 mt-1'>{coach.bio}</p>
						</div>
					</div>
				</div>
			</div>
			<div className='flex gap-4 justify-center'>
				<button
					onClick={onNext}
					className='bg-gradient-to-r from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-700 text-white px-8 py-3 rounded-xl font-semibold transition-all transform hover:scale-105 flex items-center gap-2'>
					Continue <ArrowRight size={20} />
				</button>
			</div>
		</div>
	);
};

// Date & Time Selection Step Component
const DateTimeStep = ({
	selectedDate,
	selectedTime,
	onDateSelect,
	onTimeSelect,
	onNext,
	onPrev,
	availableSlots,
	isLoadingSlots,
	slotsError,
}) => {
	const handleDateSelect = (date) => {
		// Prevent selecting past dates
		const today = new Date();
		today.setHours(0, 0, 0, 0);

		if (date < today) {
			toast.error('Cannot select past dates');
			return;
		}

		onDateSelect(date);
		// Reset time selection when date changes
		onTimeSelect(null);
	};

	const handleTimeSelect = (time) => {
		onTimeSelect(time);
	};

	return (
		<div className='space-y-6'>
			<div className='text-center'>
				<h2 className='text-3xl font-bold mb-2 bg-teal-400 bg-clip-text text-transparent'>
					Choose Your Appointment
				</h2>
				<p className='text-gray-400'>Select your preferred date and time</p>
			</div>

			<div className='flex justify-center'>
				<Calendar
					mode='single'
					selected={selectedDate}
					onSelect={handleDateSelect}
					className='rounded-xl bg-[#D9D9D9] text-black shadow-lg'
					disabled={(date) => {
						const today = new Date();
						today.setHours(0, 0, 0, 0);
						return date < today;
					}}
					modifiers={{
						today: new Date(),
					}}
					modifiersStyles={{
						today: {
							backgroundColor: '#008080',
							color: '#ffff',
							fontWeight: 'bold',
							borderRadius: '8px',
						},
					}}
				/>
			</div>

			<div className='bg-gray-800/50 backdrop-blur rounded-2xl p-6 border border-gray-700'>
				<TimeSlotSelector
					selectedDate={selectedDate}
					selectedTime={selectedTime}
					onTimeSelect={handleTimeSelect}
					availableSlots={availableSlots}
					isLoading={isLoadingSlots}
					error={slotsError}
					className='text-white'
				/>
			</div>

			<div className='flex gap-4 justify-center'>
				<button
					onClick={onPrev}
					className='bg-gray-700 hover:bg-gray-600 text-white px-6 py-3 rounded-xl font-semibold transition-all flex items-center gap-2'>
					<ArrowLeft size={20} /> Back
				</button>
				<button
					onClick={onNext}
					disabled={!selectedDate || !selectedTime}
					className='bg-gradient-to-r from-teal-500 to-teal-700 hover:from-teal-600 hover:to-teal-800 disabled:from-gray-600 disabled:to-gray-600 disabled:cursor-not-allowed text-white px-8 py-3 rounded-xl font-semibold transition-all flex items-center gap-2'>
					Continue <ArrowRight size={20} />
				</button>
			</div>
		</div>
	);
};

// Contact Information Step Component
const ContactInfoStep = ({
	formData,
	onChange,
	onBooking,
	onPrev,
	selectedDate,
	selectedTime,
	coach,
	isLoading,
	isFormValid,
}) => {
	return (
		<div className='space-y-6'>
			<div className='text-center'>
				<h2 className='text-3xl font-bold mb-2 bg-gradient-to-r from-teal-400 to-teal-500 bg-clip-text text-transparent'>
					Complete Your Booking
				</h2>
				<p className='text-gray-400'>
					We need your details to confirm the appointment
				</p>
			</div>

			<div className='dark:bg-gray-800/50 bg-white/50 backdrop-blur rounded-2xl p-6 border border-gray-700'>
				<h3 className='text-lg font-semibold mb-4 text-teal-400'>
					Appointment Summary
				</h3>
				<div className='grid grid-cols-2 gap-4 text-sm'>
					<div>
						<span className='text-gray-400'>Date:</span>
						<p className='font-medium'>
							{selectedDate?.toLocaleDateString('en-US', {
								weekday: 'long',
								year: 'numeric',
								month: 'long',
								day: 'numeric',
							})}
						</p>
					</div>
					<div>
						<span className='text-gray-400'>Time:</span>
						<p className='font-medium'>{selectedTime}</p>
					</div>
					<div>
						<span className='text-gray-400'>Coach:</span>
						<p className='font-medium'>{coach.name}</p>
					</div>
					<div>
						<span className='text-gray-400'>Session Type:</span>
						<p className='font-medium'>Life Coaching</p>
					</div>
				</div>
			</div>

			<div className='dark:bg-gray-800/50 bg-white/50 backdrop-blur rounded-2xl p-6 border border-gray-700'>
				<div className='space-y-4'>
					<div className='relative'>
						<User
							className='absolute left-3 top-3.5 text-gray-400'
							size={20}
						/>
						<input
							type='text'
							name='name'
							value={formData.name}
							onChange={onChange}
							placeholder='Enter your full name'
							className='w-full pl-12 pr-4 py-3 border border-gray-600 rounded-xl focus:border-teal-500 focus:ring-2 focus:ring-teal-500/20 outline-none transition-all'
							required
						/>
					</div>
					<div className='relative'>
						<Mail
							className='absolute left-3 top-3.5 text-gray-400'
							size={20}
						/>
						<input
							type='email'
							name='email'
							value={formData.email}
							onChange={onChange}
							placeholder='Enter your email'
							className='w-full pl-12 pr-4 py-3 border border-gray-600 rounded-xl focus:border-teal-500 focus:ring-2 focus:ring-teal-500/20 outline-none transition-all'
							required
						/>
					</div>
					<div className='relative'>
						<Phone
							className='absolute left-3 top-3.5 text-gray-400'
							size={20}
						/>
						<input
							type='tel'
							name='phone'
							value={formData.phone}
							onChange={onChange}
							placeholder='Enter your phone number'
							className='w-full pl-12 pr-4 py-3 border border-gray-600 rounded-xl focus:border-teal-500 focus:ring-2 focus:ring-teal-500/20 outline-none transition-all'
							required
						/>
					</div>
				</div>
			</div>

			<div className='flex gap-4 justify-center'>
				<button
					onClick={onPrev}
					disabled={isLoading}
					className='bg-gray-700 hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-3 rounded-xl font-semibold transition-all flex items-center gap-2'>
					<ArrowLeft size={20} /> Back
				</button>
				<button
					onClick={onBooking}
					disabled={!isFormValid || isLoading}
					className='bg-gradient-to-r from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-700 disabled:from-gray-600 disabled:to-gray-600 disabled:cursor-not-allowed text-white px-8 py-3 rounded-xl font-semibold transition-all flex items-center gap-2'>
					{isLoading ? (
						<>
							<div className='w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin' />
							Booking...
						</>
					) : (
						<>
							<Check size={20} /> Book Appointment
						</>
					)}
				</button>
			</div>
		</div>
	);
};

// Main Session Component
const Session = () => {
	const [step, setStep] = useState(1);
	const [selectedDate, setSelectedDate] = useState(null);
	const [selectedTime, setSelectedTime] = useState(null);
	const [formData, setFormData] = useState({
		name: '',
		email: '',
		phone: '',
		notes: '',
	});

	const coach = {
		name: 'Craig',
		title: 'Life Coach',
		bio: "Craig is the creator of YOU = LIFE, empowering you to live with clarity and purpose. He's your champion for transformation.",
		image: '/api/placeholder/128/128',
	};

	// Get available slots for selected date
	const {
		data: availableSlots,
		isLoading: isLoadingSlots,
		error: slotsError,
		refetch: refetchSlots,
	} = useAvailableSlots(
		selectedDate ? { date: selectedDate.toISOString().split('T')[0] } : {},
		{ enabled: !!selectedDate },
	);

	// Create booking mutation
	const createBookingMutation = useCreateBooking();

	const nextStep = () => setStep(step + 1);
	const prevStep = () => setStep(step - 1);

	const handleChange = (e) => {
		setFormData({ ...formData, [e.target.name]: e.target.value });
	};

	const handleDateSelect = (date) => {
		setSelectedDate(date);
		setSelectedTime(null); // Reset time when date changes

		// Refetch available slots for the new date
		if (date) {
			refetchSlots();
		}
	};

	const handleTimeSelect = (time) => {
		setSelectedTime(time);
	};

	const handleBooking = async () => {
		if (!isFormValid()) {
			toast.error('Please fill in all required fields');
			return;
		}

		try {
			const [hours, minutes] = selectedTime.split(':');
			const bookingDateTime = new Date(selectedDate);
			bookingDateTime.setHours(parseInt(hours), parseInt(minutes), 0, 0);

			const bookingData = {
				date: bookingDateTime,
				email: formData.email.toLowerCase(),
				bookedBy: formData.name,
				phone: formData.phone,
				notes: formData.notes,
				sessionType: 'Discovery', // Default session type
				status: 'scheduled',
			};

			await createBookingMutation.mutateAsync(bookingData);

			toast.success('Appointment booked successfully!', {
				duration: 5000,
				style: {
					background: '#10B981',
					color: '#fff',
				},
			});

			// Reset form or redirect
			setStep(1);
			setSelectedDate(null);
			setSelectedTime(null);
			setFormData({ name: '', email: '', phone: '', notes: '' });
		} catch (error) {
			console.error('Booking error:', error);

			let errorMessage = 'Failed to book appointment. Please try again.';

			if (error.message) {
				errorMessage = error.message;
			} else if (error.errors && error.errors.length > 0) {
				errorMessage = error.errors[0];
			}

			toast.error(errorMessage, {
				duration: 5000,
				style: {
					background: '#EF4444',
					color: '#fff',
				},
			});
		}
	};

	const isFormValid = () => {
		return (
			selectedDate &&
			selectedTime &&
			formData.name.trim() &&
			formData.email.trim() &&
			formData.phone.trim()
		);
	};
	const [isSidebarOpen, setIsSidebarOpen] = useState(false);
	const toggleSidebar = () => {
		setIsSidebarOpen(!isSidebarOpen);
	};

	return (
		<div className='min-h-screen '>
			<FixedHeader
				title='Session'
				toggleSidebar={toggleSidebar}
			/>
			<Toaster position='top-right' />

			<div className='max-w-2xl mx-auto p-6 pt-20'>
				<ProgressIndicator currentStep={step} />

				{step === 1 && (
					<CoachInfoStep
						coach={coach}
						onNext={nextStep}
					/>
				)}

				{step === 2 && (
					<DateTimeStep
						selectedDate={selectedDate}
						selectedTime={selectedTime}
						onDateSelect={handleDateSelect}
						onTimeSelect={handleTimeSelect}
						onNext={nextStep}
						onPrev={prevStep}
						availableSlots={availableSlots}
						isLoadingSlots={isLoadingSlots}
						slotsError={slotsError}
					/>
				)}

				{step === 3 && (
					<ContactInfoStep
						formData={formData}
						onChange={handleChange}
						onBooking={handleBooking}
						onPrev={prevStep}
						selectedDate={selectedDate}
						selectedTime={selectedTime}
						coach={coach}
						isLoading={createBookingMutation.isLoading}
						isFormValid={isFormValid}
					/>
				)}
			</div>
			<Sidebar
				isOpen={isSidebarOpen}
				onToggle={toggleSidebar}
			/>
		</div>
	);
};

export default Session;
