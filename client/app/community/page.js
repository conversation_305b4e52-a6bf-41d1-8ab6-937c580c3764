'use client';
import { useState, useCallback } from 'react';
import {
	RefreshCw,
	AlertCircle,
	MessageSquare,
	Plus,
	HomeIcon,
	UserCircleIcon,
} from 'lucide-react';

import FixedHeader from '@/src/components/layout/FixedHeader';
import Sidebar from '@/src/components/layout/Sidebar';
import PostCard from '@/src/components/community/PostCard';
import CommentModal from '@/src/components/community/CommentModal';
import Loading from '@/src/components/common/Loading';
import { useInfiniteCommunityPosts } from '@/src/lib/hooks/useInfiniteScroll';
import ProfileCarousel from '@/src/components/profiles/ProfileCarousel';
import { useGetAllUsers } from '@/src/lib/hooks';

const Community = () => {
	const [isSidebarOpen, setIsSidebarOpen] = useState(false);
	const [selectedPost, setSelectedPost] = useState(null);
	const [isCommentModalOpen, setIsCommentModalOpen] = useState(false);
	const [activeTab, setActiveTab] = useState('community');

	const {
		posts,
		isLoading,
		isLoadingMore,
		error,
		hasMore,
		reset,
		sentinelRef,
		currentUserId,
		userProfile,
		updatePostLike,
		updatePostBookmark,
		updatePostComments,
	} = useInfiniteCommunityPosts();

	// Fetch profiles data
	const {
		data: profilesResponse,
		isLoading: profilesLoading,
		error: profilesError,
	} = useGetAllUsers();

	// Extract profiles array from the response
	const profiles = profilesResponse?.data || [];

	console.log('Profiles data:', profiles);

	// Memoized callbacks
	const toggleSidebar = useCallback(() => {
		setIsSidebarOpen((prev) => !prev);
	}, []);

	const handleCommentClick = useCallback((post) => {
		setSelectedPost(post);
		setIsCommentModalOpen(true);
	}, []);

	const handleCloseCommentModal = useCallback(() => {
		setIsCommentModalOpen(false);
		setSelectedPost(null);
	}, []);

	const handleTabSwitch = useCallback((tab) => {
		setActiveTab(tab);
	}, []);

	// Error state component
	const ErrorState = () => (
		<div className='flex-1 flex items-center justify-center min-h-[60vh]'>
			<div className='max-w-md mx-auto px-6 py-8 text-center'>
				<div className='w-20 h-20 bg-red-50 rounded-full flex items-center justify-center mx-auto mb-6'>
					<AlertCircle className='w-10 h-10 text-red-500' />
				</div>
				<h3 className='text-xl font-semibold text-gray-900 mb-3'>
					Oops! Something went wrong
				</h3>
				<p className='text-gray-600 mb-6 leading-relaxed'>
					We couldn't load the community posts right now. This might be a
					temporary issue.
				</p>
			</div>
		</div>
	);

	// Empty state component
	const EmptyState = () => (
		<div className='flex-1 flex items-center justify-center min-h-[60vh]'>
			<div className='max-w-md mx-auto px-6 py-8 text-center'>
				<div className='w-20 h-20 bg-gradient-to-br from-teal-50 to-blue-50 rounded-full flex items-center justify-center mx-auto mb-6'>
					<MessageSquare className='w-10 h-10 text-teal-500' />
				</div>
				<h3 className='text-xl font-semibold text-gray-900 mb-3'>
					Welcome to the Community!
				</h3>
				<p className='text-gray-600 mb-6 leading-relaxed'>
					This is where you can share your goals, values, and life missions with
					others. Be the first to start the conversation!
				</p>
			</div>
		</div>
	);

	// Loading skeletons with improved styling
	const LoadingSkeletons = () => (
		<div className='space-y-6'>
			{Array.from({ length: 3 }, (_, i) => (
				<div
					key={i}
					className='bg-white border border-gray-100 rounded-xl p-6 shadow-sm'>
					<Loading.Skeleton lines={3} />
				</div>
			))}
		</div>
	);

	// Tab navigation component
	const TabNavigation = () => (
		<div className='flex items-center justify-center mb-8 max-w-2xl mx-auto px-4'>
			<div className='bg-white rounded-2xl p-2 shadow-lg border border-gray-100'>
				<div className='flex items-center gap-4'>
					{/* Community Tab */}
					<button
						onClick={() => handleTabSwitch('community')}
						className={`flex items-center gap-3 px-6 py-3 rounded-xl transition-all duration-200 cursor-pointer ${
							activeTab === 'community'
								? 'bg-teal-500 text-white shadow-md'
								: 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
						}`}>
						<HomeIcon
							size={20}
							className={
								activeTab === 'community' ? 'text-white' : 'text-gray-500'
							}
						/>
						<span className='font-medium'>Community</span>
					</button>

					{/* Profiles Tab */}
					<button
						onClick={() => handleTabSwitch('profiles')}
						className={`flex items-center gap-3 px-6 py-3 rounded-xl transition-all duration-200 cursor-pointer ${
							activeTab === 'profiles'
								? 'bg-teal-500 text-white shadow-md'
								: 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
						}`}>
						<UserCircleIcon
							size={20}
							className={
								activeTab === 'profiles' ? 'text-white' : 'text-gray-500'
							}
						/>
						<span className='font-medium'>Profiles</span>
					</button>
				</div>
			</div>
		</div>
	);

	// Community content component
	const CommunityContent = () => {
		if (error) {
			return <ErrorState />;
		}

		if (isLoading) {
			return <LoadingSkeletons />;
		}

		if (posts.length === 0) {
			return <EmptyState />;
		}

		return (
			<>
				{/* Posts Container */}
				<div className='space-y-6 pb-8'>
					{posts.map((post, index) => (
						<div
							key={post.id}
							className='transform transition-all duration-300 hover:scale-[1.02]'
							style={{
								animationDelay: `${index * 100}ms`,
								animation: 'fadeInUp 0.6s ease-out forwards',
							}}>
							<PostCard
								post={post}
								onCommentClick={handleCommentClick}
								currentUserId={currentUserId}
								userProfile={userProfile}
								updatePostLike={updatePostLike}
								updatePostBookmark={updatePostBookmark}
								updatePostComments={updatePostComments}
							/>
						</div>
					))}
				</div>

				{/* Loading More Indicator */}
				{isLoadingMore && (
					<div className='py-8 flex justify-center'>
						<div className='flex items-center space-x-3 px-6 py-3 bg-white rounded-full shadow-sm border border-gray-100'>
							<div className='w-5 h-5 border-2 border-teal-200 border-t-teal-600 rounded-full animate-spin'></div>
							<span className='text-sm font-medium text-gray-600'>
								Loading more posts...
							</span>
						</div>
					</div>
				)}

				{/* Infinite Scroll Sentinel */}
				{hasMore && (
					<div
						ref={sentinelRef}
						className='h-20 flex items-center justify-center'
					/>
				)}

				{/* End of Posts Message */}
				{!hasMore && posts.length > 0 && (
					<div className='py-12 text-center'>
						<h4 className='text-lg font-semibold text-gray-900 mb-2'>
							You're all caught up!
						</h4>
						<p className='text-gray-500 mb-4'>
							Check back later for new posts from the community.
						</p>
					</div>
				)}
			</>
		);
	};

	// Profiles content component with error handling and loading states
	const ProfilesContent = () => {
		if (profilesError) {
			return (
				<div className='flex-1 flex items-center justify-center min-h-[60vh]'>
					<div className='max-w-md mx-auto px-6 py-8 text-center'>
						<div className='w-20 h-20 bg-red-50 rounded-full flex items-center justify-center mx-auto mb-6'>
							<AlertCircle className='w-10 h-10 text-red-500' />
						</div>
						<h3 className='text-xl font-semibold text-gray-900 mb-3'>
							Failed to load profiles
						</h3>
						<p className='text-gray-600 mb-6 leading-relaxed'>
							We couldn't load the user profiles right now. Please try again
							later.
						</p>
					</div>
				</div>
			);
		}

		if (profilesLoading) {
			return (
				<div className='space-y-6'>
					<div className='bg-white border border-gray-100 rounded-xl p-6 shadow-sm'>
						<Loading.Skeleton lines={2} />
					</div>
				</div>
			);
		}

		if (!profiles || profiles.length === 0) {
			return (
				<div className='flex-1 flex items-center justify-center min-h-[60vh]'>
					<div className='max-w-md mx-auto px-6 py-8 text-center'>
						<div className='w-20 h-20 bg-gradient-to-br from-purple-50 to-pink-50 rounded-full flex items-center justify-center mx-auto mb-6'>
							<UserCircleIcon className='w-10 h-10 text-purple-500' />
						</div>
						<h3 className='text-xl font-semibold text-gray-900 mb-3'>
							No profiles found
						</h3>
						<p className='text-gray-600 mb-6 leading-relaxed'>
							There are no user profiles to display at the moment.
						</p>
					</div>
				</div>
			);
		}

		return (
			<div className='space-y-6'>
				<ProfileCarousel
					profiles={profiles}
					isLoading={profilesLoading}
					error={profilesError}
				/>
				{/* Add any additional profile-related content here */}
			</div>
		);
	};

	return (
		<div className='min-h-screen'>
			<FixedHeader
				title={activeTab === 'community' ? 'Community' : 'Profiles'}
				toggleSidebar={toggleSidebar}
			/>

			<div className='pt-16'>
				{/* Tab Navigation */}
				<TabNavigation />

				{/* Main Content Container */}
				<div className='max-w-2xl mx-auto px-4 sm:px-6 lg:px-8'>
					{activeTab === 'community' ? (
						<CommunityContent />
					) : (
						<ProfilesContent />
					)}
				</div>
			</div>

			{/* Modals */}
			<CommentModal
				post={selectedPost}
				isOpen={isCommentModalOpen}
				onClose={handleCloseCommentModal}
			/>

			<Sidebar
				isOpen={isSidebarOpen}
				onToggle={toggleSidebar}
			/>
		</div>
	);
};

export default Community;
