'use client';
import { sunset } from '@/assets/images';
import { CustomImage } from '@/src/components/common';
import FixedHeader from '@/src/components/layout/FixedHeader';
import Sidebar from '@/src/components/layout/Sidebar';
import { lifeMissionService } from '@/src/lib/api/lifeMission';
import { Share2 } from 'lucide-react';
import React, { useState } from 'react';
import toast from 'react-hot-toast';

const LifeMission = () => {
	const [isSidebarOpen, setIsSidebarOpen] = useState(false);
	const [mission, setMission] = useState('');
	const [lifeMissionId, setLifeMissionId] = useState(null); // set after save
	const [visibleOnProfile, setVisibleOnProfile] = useState(false);

	const toggleSidebar = () => {
		setIsSidebarOpen(!isSidebarOpen);
	};

	const handleSave = async () => {
		if (!mission.trim())
			return toast.error('📝 Please write your life mission');

		try {
			const res = await lifeMissionService.createLifeMission({
				mission,
				isPublic: false,
			});
			setMission('');
			setLifeMissionId(res.data.lifeMission._id);
			toast.success('✅ Saved privately! 🚀');
		} catch (err) {
			console.error(err);
			toast.error(`❌ ${err.message || 'Failed to save'}`);
		}
	};

	const handleShare = async () => {
		if (!mission.trim())
			return toast.error('📝 Please write your life mission');

		try {
			// Not saved yet – create & share
			const res = await lifeMissionService.createLifeMission({
				mission,
				isPublic: true,
			});
			setMission('');
			setLifeMissionId(res.data.lifeMission._id);
			toast.success('🌍 Shared with the community! 🎉');
			setVisibleOnProfile(true);
		} catch (err) {
			console.error(err);
			toast.error(`❌ ${err.message || 'Failed to share'}`);
		}
	};

	return (
		<div className='flex flex-col min-h-screen bg-gray-50 dark:bg-gray-900'>
			<FixedHeader
				title='Life Mission'
				toggleSidebar={toggleSidebar}
			/>

			<div className='flex-1 flex justify-center items-start w-full py-8 px-4'>
				<div className='w-full max-w-3xl bg-white dark:bg-gray-800 rounded-xl shadow-2xl overflow-hidden'>
					<CustomImage
						src={sunset}
						alt='Sunset'
						width={800}
						height={300}
						className='object-cover w-full h-52 md:h-64'
					/>

					<div className='p-6 md:p-8'>
						<div className='mb-6'>
							<h4 className='text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-3'>
								What does your ideal future look like?
							</h4>
							<div className='bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border-l-4 border-blue-500'>
								<p className='text-sm font-semibold text-blue-700 dark:text-blue-300 mb-2'>
									💡 Hint
								</p>
								<div className='space-y-2 text-gray-700 dark:text-gray-300'>
									<p>
										• In 5/10/20 years, what kind of impact do you want to have?
									</p>
									<p>
										• What are the most important experiences you want to
										create?
									</p>
									<p>• How do you want to be remembered?</p>
								</div>
							</div>
						</div>

						<textarea
							className='w-full p-4 rounded-xl bg-gray-50 dark:bg-gray-700 border-2 border-gray-200 dark:border-gray-600 text-gray-900 dark:text-white outline-none resize-none placeholder:text-gray-400 placeholder:text-base focus:border-blue-500 dark:focus:border-blue-400 transition-colors'
							placeholder='Share your vision for the future...'
							rows={8}
							value={mission}
							onChange={(e) => setMission(e.target.value)}
						/>

						<div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mt-6'>
							<div className='flex items-center gap-3'>
								<button
									onClick={handleShare}
									className='flex items-center gap-2 px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-lg font-medium transition-colors duration-200 cursor-pointer'>
									<Share2 size={18} />
									Share on Community
								</button>

								<button
									onClick={handleSave}
									className='px-6 py-2 bg-teal-600 hover:bg-teal-700 dark:bg-teal-500 dark:hover:bg-teal-600 text-white rounded-lg font-semibold transition-colors duration-200 cursor-pointer'>
									Save
								</button>
							</div>
						</div>
					</div>
				</div>
			</div>

			<Sidebar
				isOpen={isSidebarOpen}
				onToggle={toggleSidebar}
			/>
		</div>
	);
};

export default LifeMission;
