'use client';
import { use, useState } from 'react';
import { useRouter } from 'next/navigation';
import { CustomImage, ImageModal, Loading } from '@/src/components/common';
import FixedHeader from '@/src/components/layout/FixedHeader';
import Sidebar from '@/src/components/layout/Sidebar';
import ProtectedRoute from '@/src/components/layout/ProtectedRoute';
import { useGetUserById } from '@/src/lib/hooks';
import { useDarkMode } from '@/contexts/DarkModeContext';
import {
	FaArrowLeft,
	FaEnvelope,
	FaPhone,
	FaMapMarkerAlt,
	FaCalendarAlt,
	FaUser,
	FaExclamationTriangle,
} from 'react-icons/fa';

const UserProfileDetail = ({ params }) => {
	const resolvedParams = use(params);
	const router = useRouter();
	useDarkMode(); // For theme context
	const [isSidebarOpen, setIsSidebarOpen] = useState(false);
	const [isImageModalOpen, setIsImageModalOpen] = useState(false);

	const {
		data: userProfile,
		isLoading,
		error,
	} = useGetUserById(resolvedParams.id);

	const toggleSidebar = () => {
		setIsSidebarOpen(!isSidebarOpen);
	};

	const handleGoBack = () => {
		router.back();
	};

	const handleAvatarClick = () => {
		if (userProfile?.user?.avatar) {
			setIsImageModalOpen(true);
		}
	};

	const formatDate = (dateString) => {
		if (!dateString) return 'Not provided';
		return new Date(dateString).toLocaleDateString('en-US', {
			year: 'numeric',
			month: 'long',
			day: 'numeric',
		});
	};

	const formatLocation = (location) => {
		if (!location) return 'Not provided';
		const parts = [location.city, location.state, location.country].filter(
			Boolean,
		);
		return parts.length > 0 ? parts.join(', ') : 'Not provided';
	};

	if (isLoading) {
		return (
			<ProtectedRoute>
				<div className='min-h-screen bg-white dark:bg-[#0C142A] transition-colors duration-300'>
					<FixedHeader
						title='User Profile'
						toggleSidebar={toggleSidebar}
					/>
					<div className='container mx-auto px-4 py-8 pt-20'>
						<Loading />
					</div>
				</div>
			</ProtectedRoute>
		);
	}

	if (error) {
		return (
			<ProtectedRoute>
				<div className='min-h-screen bg-white dark:bg-[#0C142A] transition-colors duration-300'>
					<FixedHeader
						title='User Profile'
						toggleSidebar={toggleSidebar}
					/>
					<div className='container mx-auto px-4 py-8 pt-20'>
						<div className='flex flex-col items-center justify-center py-12'>
							<FaExclamationTriangle className='text-red-500 text-6xl mb-4' />
							<h2 className='text-2xl font-bold text-gray-800 dark:text-gray-200 mb-2'>
								Profile Not Found
							</h2>
							<p className='text-gray-600 dark:text-gray-400 mb-6 text-center'>
								{error?.response?.status === 404
									? 'The user profile you are looking for does not exist.'
									: 'Failed to load user profile. Please try again later.'}
							</p>
							<button
								onClick={handleGoBack}
								className='bg-teal-500 hover:bg-teal-600 text-white px-6 py-3 rounded-lg transition-colors duration-200 flex items-center gap-2'>
								<FaArrowLeft />
								Go Back
							</button>
						</div>
					</div>
				</div>
			</ProtectedRoute>
		);
	}

	const user = userProfile?.user;

	return (
		<ProtectedRoute>
			<div className='min-h-screen bg-white dark:bg-[#0C142A] transition-colors duration-300'>
				<FixedHeader
					title='User Profile'
					toggleSidebar={toggleSidebar}
				/>
				<Sidebar
					isOpen={isSidebarOpen}
					onClose={() => setIsSidebarOpen(false)}
				/>

				<div className='container mx-auto px-4 py-8 pt-20'>
					{/* Back Button */}
					<button
						onClick={handleGoBack}
						className='mb-6 flex items-center gap-2 text-teal-600 hover:text-teal-700 dark:text-teal-400 dark:hover:text-teal-300 transition-colors duration-200'>
						<FaArrowLeft />
						<span>Back</span>
					</button>

					{/* Profile Header */}
					<div className='flex flex-col items-center mb-8'>
						<div
							className='w-32 h-32 rounded-full overflow-hidden shadow-lg ring-4 ring-white dark:ring-gray-800 hover:ring-teal-200 dark:hover:ring-teal-800 transition-all duration-200 cursor-pointer relative'
							onClick={handleAvatarClick}>
							<CustomImage
								src={user?.avatar}
								alt={`${user?.name}'s Avatar`}
								width={128}
								height={128}
								className='w-full h-full object-cover hover:scale-110 transition-transform duration-200'
							/>
						</div>
						<h1 className='mt-4 text-3xl font-bold text-gray-800 dark:text-gray-200'>
							{user?.name || 'Unknown User'}
						</h1>
						{user?.aboutMe && (
							<p className='mt-2 text-gray-600 dark:text-gray-400 text-center max-w-2xl'>
								{user.aboutMe}
							</p>
						)}
					</div>

					{/* Profile Details */}
					<div className='max-w-4xl mx-auto'>
						<div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
							{/* Contact Information */}
							<div className='bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 transition-colors duration-300'>
								<h3 className='text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4 flex items-center gap-2'>
									<FaUser className='text-teal-500' />
									Contact Information
								</h3>
								<div className='space-y-4'>
									<div className='flex items-center gap-3'>
										<FaEnvelope className='text-gray-500 dark:text-gray-400' />
										<div>
											<label className='text-sm text-gray-600 dark:text-gray-400 block'>
												Email
											</label>
											<p className='text-gray-800 dark:text-gray-200'>
												{user?.email || 'Not provided'}
											</p>
										</div>
									</div>
									<div className='flex items-center gap-3'>
										<FaPhone className='text-gray-500 dark:text-gray-400' />
										<div>
											<label className='text-sm text-gray-600 dark:text-gray-400 block'>
												Phone
											</label>
											<p className='text-gray-800 dark:text-gray-200'>
												{user?.phone || user?.mobile || 'Not provided'}
											</p>
										</div>
									</div>
								</div>
							</div>

							{/* Personal Information */}
							<div className='bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 transition-colors duration-300'>
								<h3 className='text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4 flex items-center gap-2'>
									<FaCalendarAlt className='text-teal-500' />
									Personal Information
								</h3>
								<div className='space-y-4'>
									<div className='flex items-center gap-3'>
										<FaMapMarkerAlt className='text-gray-500 dark:text-gray-400' />
										<div>
											<label className='text-sm text-gray-600 dark:text-gray-400 block'>
												Location
											</label>
											<p className='text-gray-800 dark:text-gray-200'>
												{formatLocation(user?.location)}
											</p>
										</div>
									</div>
									<div className='flex items-center gap-3'>
										<FaCalendarAlt className='text-gray-500 dark:text-gray-400' />
										<div>
											<label className='text-sm text-gray-600 dark:text-gray-400 block'>
												Date of Birth
											</label>
											<p className='text-gray-800 dark:text-gray-200'>
												{formatDate(user?.dateOfBirth)}
											</p>
										</div>
									</div>
								</div>
							</div>
						</div>

						{/* Account Information */}
						<div className='mt-6 bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 transition-colors duration-300'>
							<h3 className='text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4'>
								Account Information
							</h3>
							<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
								<div>
									<label className='text-sm text-gray-600 dark:text-gray-400 block'>
										Status
									</label>
									<span
										className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
											user?.status === 'active'
												? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
												: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
										}`}>
										{user?.status || 'Unknown'}
									</span>
								</div>
								<div>
									<label className='text-sm text-gray-600 dark:text-gray-400 block'>
										Email Verified
									</label>
									<span
										className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
											user?.isEmailVerified
												? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
												: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
										}`}>
										{user?.isEmailVerified ? 'Verified' : 'Not Verified'}
									</span>
								</div>
								<div>
									<label className='text-sm text-gray-600 dark:text-gray-400 block'>
										Member Since
									</label>
									<p className='text-gray-800 dark:text-gray-200'>
										{formatDate(user?.createdAt)}
									</p>
								</div>
								<div>
									<label className='text-sm text-gray-600 dark:text-gray-400 block'>
										Last Login
									</label>
									<p className='text-gray-800 dark:text-gray-200'>
										{formatDate(user?.lastLogin)}
									</p>
								</div>
							</div>
						</div>
					</div>
				</div>

				{/* Image Modal */}
				<ImageModal
					isOpen={isImageModalOpen}
					onClose={() => setIsImageModalOpen(false)}
					src={user?.avatar}
					alt={`${user?.name}'s Profile Picture`}
					title={`${user?.name}'s Profile Picture`}
				/>
			</div>
		</ProtectedRoute>
	);
};

export default UserProfileDetail;
