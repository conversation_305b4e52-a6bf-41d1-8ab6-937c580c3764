'use client';
import { Pagination } from '@/src/components/common';
import AdminLayout from '@/src/components/layout/AdminLayout';
import React, { useState, useEffect } from 'react';

import {
	useBookings,
	useUpdateBookingStatus,
	useCancelBooking,
	useDeleteBooking,
} from '@/src/lib/hooks/useBookings';
import {
	BookingStatusBadge,
	BookingActionMenu,
	BookingSearchFilter,
} from '@/src/components/features/booking';
import toast from 'react-hot-toast';

const BookingManagement = () => {
	const [currentPage, setCurrentPage] = useState(1);
	const [searchTerm, setSearchTerm] = useState('');
	const [filters, setFilters] = useState({
		status: '',
		sessionType: '',
		dateRange: '',
	});
	const [selectedBooking, setSelectedBooking] = useState(null);
	const [showViewModal, setShowViewModal] = useState(false);
	const [showDeleteModal, setShowDeleteModal] = useState(false);
	const [bookingToDelete, setBookingToDelete] = useState(null);

	const limit = 10;

	// Build query parameters for API call
	const queryParams = {
		page: currentPage,
		limit,
		...(searchTerm && {
			// Search in both email and bookedBy fields
			email: searchTerm,
			bookedBy: searchTerm,
		}),
		...(filters.status && { status: filters.status }),
		...(filters.sessionType && { sessionType: filters.sessionType }),
	};

	// Get bookings data
	const {
		data: bookingsData,
		isLoading,
		error,
		refetch,
	} = useBookings(queryParams);

	// Mutations
	const updateStatusMutation = useUpdateBookingStatus();
	const cancelBookingMutation = useCancelBooking();
	const deleteBookingMutation = useDeleteBooking();

	const bookings = bookingsData?.data || [];
	const totalPages = bookingsData?.pagination?.totalPages || 1;

	// Handle search
	const handleSearch = (term) => {
		setSearchTerm(term);
		setCurrentPage(1); // Reset to first page when searching
	};

	// Handle filter
	const handleFilter = (newFilters) => {
		setFilters(newFilters);
		setCurrentPage(1); // Reset to first page when filtering
	};

	// Handle view booking
	const handleViewBooking = (booking) => {
		setSelectedBooking(booking);
		setShowViewModal(true);
	};

	// Handle edit booking (placeholder for future implementation)
	const handleEditBooking = () => {
		toast.info('Edit functionality coming soon');
	};

	// Handle delete booking
	const handleDeleteBooking = (booking) => {
		setBookingToDelete(booking);
		setShowDeleteModal(true);
	};

	// Handle cancel booking
	const handleCancelBooking = async (booking) => {
		try {
			await cancelBookingMutation.mutateAsync(booking._id);
			toast.success('Booking cancelled successfully');
			refetch();
		} catch (error) {
			toast.error(error.message || 'Failed to cancel booking');
		}
	};

	// Handle mark as complete
	const handleMarkComplete = async (booking) => {
		try {
			await updateStatusMutation.mutateAsync({
				id: booking._id,
				status: 'completed',
			});
			toast.success('Booking marked as completed');
			refetch();
		} catch (error) {
			toast.error(error.message || 'Failed to update booking status');
		}
	};

	// Confirm delete booking
	const confirmDeleteBooking = async () => {
		if (!bookingToDelete) return;

		try {
			await deleteBookingMutation.mutateAsync(bookingToDelete._id);
			toast.success('Booking deleted successfully');
			setShowDeleteModal(false);
			setBookingToDelete(null);
			refetch();
		} catch (error) {
			toast.error(error.message || 'Failed to delete booking');
		}
	};

	// Format date and time for display
	const formatDateTime = (date) => {
		if (!date) return 'N/A';
		const d = new Date(date);
		return (
			d.toLocaleDateString('en-GB', {
				day: '2-digit',
				month: '2-digit',
				year: 'numeric',
			}) +
			' ' +
			d.toLocaleTimeString('en-US', {
				hour: '2-digit',
				minute: '2-digit',
				hour12: true,
			})
		);
	};

	return (
		<AdminLayout>
			<div className='p-6 bg-cyan-100 text-black min-h-screen'>
				{/* Header */}
				<div className='mb-6'>
					<h1 className='text-2xl font-bold text-gray-900 mb-2'>
						Booking Management
					</h1>
					<p className='text-gray-600'>Manage all coaching session bookings</p>
				</div>

				{/* Search and Filter */}
				<div className='bg-white rounded-lg shadow p-6 mb-6'>
					<BookingSearchFilter
						onSearch={handleSearch}
						onFilter={handleFilter}
						searchPlaceholder='Search bookings by name, email, or session type...'
						showFilters={true}
					/>
				</div>

				{/* Loading State */}
				{isLoading && (
					<div className='text-center py-12'>
						<div className='w-8 h-8 border-2 border-teal-500 border-t-transparent rounded-full animate-spin mx-auto mb-4' />
						<p className='text-gray-600'>Loading bookings...</p>
					</div>
				)}

				{/* Error State */}
				{error && (
					<div className='text-center py-12'>
						<div className='bg-red-50 border border-red-200 rounded-lg p-6'>
							<p className='text-red-600 mb-4'>Failed to load bookings</p>
							<button
								onClick={() => refetch()}
								className='bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600 transition-colors'>
								Try Again
							</button>
						</div>
					</div>
				)}

				{/* Table */}
				{!isLoading && !error && (
					<div className=' rounded-md shadow'>
						<table className='w-full bg-white text-sm'>
							<thead>
								<tr className='bg-blue-100 text-left text-gray-800'>
									<th className='px-4 py-3 font-semibold'>Date & Time</th>
									<th className='px-4 py-3 font-semibold'>User</th>
									<th className='px-4 py-3 font-semibold'>Session</th>
									<th className='px-4 py-3 font-semibold'>Status</th>
									<th className='px-4 py-3 font-semibold'>Actions</th>
								</tr>
							</thead>
							<tbody>
								{bookings.length === 0 ? (
									<tr>
										<td
											colSpan={5}
											className='px-4 py-8 text-center text-gray-500'>
											No bookings found
										</td>
									</tr>
								) : (
									bookings.map((booking, index) => (
										<tr
											key={booking._id}
											className={`${
												index % 2 === 0 ? 'bg-gray-50' : 'bg-white'
											} hover:bg-gray-100 transition-colors`}>
											<td className='px-4 py-3'>
												{formatDateTime(booking.date)}
											</td>
											<td className='px-4 py-3'>
												<div>
													<div className='font-medium'>
														{booking.bookedBy || 'N/A'}
													</div>
													<div className='text-gray-500 text-xs'>
														{booking.email}
													</div>
												</div>
											</td>
											<td className='px-4 py-3'>
												{booking.sessionType || 'Discovery'}
											</td>
											<td className='px-4 py-3'>
												<BookingStatusBadge status={booking.status} />
											</td>
											<td className='px-4 py-3'>
												<div className='flex items-center space-x-2'>
													<button
														onClick={() => handleViewBooking(booking)}
														className='text-blue-600 hover:text-blue-800 text-sm px-2 py-1 rounded hover:bg-blue-50 transition-colors'>
														View
													</button>
													<button
														onClick={() => handleCancelBooking(booking)}
														disabled={
															booking.status === 'cancelled' ||
															booking.status === 'completed'
														}
														className='text-orange-600 hover:text-orange-800 text-sm px-2 py-1 rounded hover:bg-orange-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed'>
														Cancel
													</button>
													<BookingActionMenu
														booking={booking}
														onView={handleViewBooking}
														onEdit={handleEditBooking}
														onDelete={handleDeleteBooking}
														onCancel={handleCancelBooking}
														onMarkComplete={handleMarkComplete}
													/>
												</div>
											</td>
										</tr>
									))
								)}
							</tbody>
						</table>
					</div>
				)}

				{/* Pagination */}
				{!isLoading && !error && totalPages > 1 && (
					<div className='mt-6'>
						<Pagination
							currentPage={currentPage}
							totalPages={totalPages}
							onPageChange={setCurrentPage}
						/>
					</div>
				)}
			</div>

			{/* View Booking Modal */}
			{showViewModal && selectedBooking && (
				<div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
					<div className='bg-white rounded-lg p-6 max-w-md w-full mx-4 max-h-[80vh]'>
						<div className='flex justify-between items-center mb-4'>
							<h3 className='text-lg font-semibold text-gray-900'>
								Booking Details
							</h3>
							<button
								onClick={() => setShowViewModal(false)}
								className='text-gray-400 hover:text-gray-600'>
								×
							</button>
						</div>
						<div className='space-y-3'>
							<div>
								<label className='text-sm font-medium text-gray-600'>
									Date & Time
								</label>
								<p className='text-gray-900'>
									{formatDateTime(selectedBooking.date)}
								</p>
							</div>
							<div>
								<label className='text-sm font-medium text-gray-600'>
									User
								</label>
								<p className='text-gray-900'>
									{selectedBooking.bookedBy || 'N/A'}
								</p>
							</div>
							<div>
								<label className='text-sm font-medium text-gray-600'>
									Email
								</label>
								<p className='text-gray-900'>{selectedBooking.email}</p>
							</div>
							<div>
								<label className='text-sm font-medium text-gray-600'>
									Phone
								</label>
								<p className='text-gray-900'>
									{selectedBooking.phone || 'N/A'}
								</p>
							</div>
							<div>
								<label className='text-sm font-medium text-gray-600'>
									Session Type
								</label>
								<p className='text-gray-900'>
									{selectedBooking.sessionType || 'Discovery'}
								</p>
							</div>
							<div>
								<label className='text-sm font-medium text-gray-600'>
									Status
								</label>
								<div className='mt-1'>
									<BookingStatusBadge status={selectedBooking.status} />
								</div>
							</div>
							<div>
								<label className='text-sm font-medium text-gray-600'>
									Created
								</label>
								<p className='text-gray-900'>
									{formatDateTime(selectedBooking.createdAt)}
								</p>
							</div>
						</div>
						<div className='flex justify-end mt-6'>
							<button
								onClick={() => setShowViewModal(false)}
								className='px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors'>
								Close
							</button>
						</div>
					</div>
				</div>
			)}

			{/* Delete Confirmation Modal */}
			{showDeleteModal && (
				<div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
					<div className='bg-white rounded-lg p-6 max-w-md w-full mx-4'>
						<h3 className='text-lg font-semibold text-gray-900 mb-4'>
							Delete Booking
						</h3>
						<p className='text-gray-600 mb-6'>
							Are you sure you want to delete this booking? This action cannot
							be undone.
						</p>
						<div className='flex justify-end space-x-4'>
							<button
								onClick={() => setShowDeleteModal(false)}
								className='px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors'>
								Cancel
							</button>
							<button
								onClick={confirmDeleteBooking}
								disabled={deleteBookingMutation.isLoading}
								className='px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors disabled:opacity-50'>
								{deleteBookingMutation.isLoading ? 'Deleting...' : 'Delete'}
							</button>
						</div>
					</div>
				</div>
			)}
		</AdminLayout>
	);
};

export default BookingManagement;
