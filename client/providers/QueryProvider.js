'use client';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useState } from 'react';

export default function QueryProvider({ children }) {
	// Check if we're in development mode
	const isDevelopment = process.env.NODE_ENV === 'development';

	// Create a client with default configuration
	const [queryClient] = useState(
		() =>
			new QueryClient({
				defaultOptions: {
					queries: {
						// In development: disable caching for immediate updates
						// In production: normal caching behavior
						staleTime: isDevelopment ? 0 : 5 * 60 * 1000, // 0 in dev, 5 minutes in prod
						gcTime: isDevelopment ? 0 : 10 * 60 * 1000, // 0 in dev, 10 minutes in prod
						// Retry failed requests
						retry: isDevelopment ? 0 : 1, // No retries in dev for faster feedback
						// Refetch on window focus - enabled in dev for fresh data
						refetchOnWindowFocus: isDevelopment,
						// Refetch on reconnect - enabled in dev
						refetchOnReconnect: isDevelopment,
						// Refetch on mount - always in dev
						refetchOnMount: isDevelopment ? 'always' : true,
					},
					mutations: {
						// Retry failed mutations
						retry: isDevelopment ? 0 : 1,
					},
				},
			}),
	);

	return (
		<QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
	);
}
